#include "NiagaraToolsModule.h"
#include "NiagaraSystem.h"
#include "Modules/ModuleManager.h"
#include "v2/ReadNiagaraSystem_v2.h"

#define LOCTEXT_NAMESPACE "FNewModuleModule"

void FNiagaraToolsModule::StartupModule()
{
    // Register for post-engine init callback
    FCoreDelegates::OnPostEngineInit.AddRaw(this, &FNiagaraToolsModule::OnPostEngineInit);
}

void FNiagaraToolsModule::ShutdownModule()
{
    // Clean up the delegate
    FCoreDelegates::OnPostEngineInit.RemoveAll(this);
}

void FNiagaraToolsModule::OnPostEngineInit()
{
}

void FNiagaraToolsModule::OnIncreaseSizeRequested(UNiagaraSystem* NiagaraSystem)
{
    if (NiagaraSystem)
    {
        UReadNiagaraSystem_v2::ModifyNiagaraSizeParameters(NiagaraSystem, 1.25f);
    }
}

void FNiagaraToolsModule::OnDecreaseSizeRequested(UNiagaraSystem* NiagaraSystem)
{
    if (NiagaraSystem)
    {
        UReadNiagaraSystem_v2::ModifyNiagaraSizeParameters(NiagaraSystem, 0.75f);
    }
}

#undef LOCTEXT_NAMESPACE
    
IMPLEMENT_MODULE(FNiagaraToolsModule, NiagaraToolsModule)