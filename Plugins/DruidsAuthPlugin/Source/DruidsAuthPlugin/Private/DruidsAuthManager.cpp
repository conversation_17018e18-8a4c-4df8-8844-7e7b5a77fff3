#include "DruidsAuthManager.h"
#include "SimpleHttpLoopbackServer.h"

#include "Misc/Guid.h"
#include "HAL/PlatformProcess.h"
#include "TimerManager.h"

TWeakObjectPtr<UDruidsAuthManager> UDruidsAuthManager::Singleton;

UDruidsAuthManager* UDruidsAuthManager::Get()
{
	if (!Singleton.IsValid())
	{
		UDruidsAuthManager* Obj = NewObject<UDruidsAuthManager>(GetTransientPackage());
		Obj->AddToRoot();
		Singleton = Obj;
	}
	return Singleton.Get();
}

bool UDruidsAuthManager::IsLoggedIn() const
{
	// Token.IsValid() returns true when we have a non-empty access-token
	// and the expiry timestamp is still in the future.
	return Token.IsValid();
}

void UDruidsAuthManager::Logout()
{
	// Wipe everything in memory
	Token = FDruidsTokenInfo{};

	// Remove from the user’s INI so the next editor run starts clean
	ClearTokenConfig();

	UE_LOG(LogTemp, Log, TEXT("DruidsAuthManager: user logged-out"));
}

void UDruidsAuthManager::ValidateUserAsync(const FOnDruidsAuthComplete& CompletionDelegate)
{
	Completion = CompletionDelegate;
	LoadTokenFromConfig();
	
	if (Token.IsValid())                      // ✅ fast-path
	{
		FDruidsAuthResult R;
		R.Status  = EDruidsAuthStatus::Success;
		R.IdToken = Token.AccessToken;
		Finish(R);
		return;
	}

	if (!Token.RefreshToken.IsEmpty())        // ↻ try silent refresh first
	{
		RefreshTokenAsync();
		return;
	}

	// Start web login flow if the tokens are invalid
	StateNonce = RandNonce();
	if (!StartServerPickPort())
	{
		FDruidsAuthResult R;
		R.Status = EDruidsAuthStatus::Failed;
		R.Message = TEXT("Failed to start loopback listener");
		Finish(R);
		return;
	}

	const FString AuthUrl = BuildLoginAuthUrl();
	FPlatformProcess::LaunchURL(*AuthUrl, nullptr, nullptr);

	if (TimeoutSeconds > 0)
	{
		TimeoutDeadlineSec = FPlatformTime::Seconds() + double(TimeoutSeconds);

		// Tick every 0.1s (adjust as you like)
		TimeoutTickerHandle = FTSTicker::GetCoreTicker().AddTicker(
			FTickerDelegate::CreateWeakLambda(this, [this](float DeltaSeconds)
			{
				if (bFinished) return false; // stop ticking if already finished

				if (FPlatformTime::Seconds() >= TimeoutDeadlineSec)
				{
					FDruidsAuthResult R;
					R.Status  = EDruidsAuthStatus::Canceled;
					R.Message = TEXT("Login timed out");
					Finish(R);
					return false; // remove ticker
				}
				return true; // keep ticking
			}),
			0.1f);
	}
}

void UDruidsAuthManager::RefreshTokenAsync()
{
	UE_LOG(LogTemp, Log, TEXT("Refreshing Druids token"));

	TSharedRef<IHttpRequest, ESPMode::ThreadSafe> Req =
		FHttpModule::Get().CreateRequest();

	Req->SetURL(BaseUrl + RefreshPath); // /api/auth/refresh
	Req->SetVerb(TEXT("POST"));
	Req->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	Req->SetContentAsString(
		FString::Printf(TEXT(R"({"refresh_token":"%s"})"), *Token.RefreshToken));

	Req->OnProcessRequestComplete().BindUObject(
		this, &UDruidsAuthManager::HandleRefreshResponse);
	Req->ProcessRequest();
}

void UDruidsAuthManager::HandleRefreshResponse(FHttpRequestPtr, FHttpResponsePtr Res,
											   bool bSucceeded)
{
	if (!bSucceeded || !Res.IsValid() || Res->GetResponseCode() != 200)
	{
		UE_LOG(LogTemp, Warning, TEXT("Refresh failed, falling back to login UI"));
		Token = {};              // clear in-memory copy
		ClearTokenConfig();
		ValidateUserAsync(Completion); // recurse → interactive flow
		return;
	}

	TSharedPtr<FJsonObject> Json;
	const TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Res->GetContentAsString());
	if (FJsonSerializer::Deserialize(Reader, Json) && Json.IsValid())
	{
		Token.AccessToken  = Json->GetStringField(TEXT("access_token"));
		Token.RefreshToken = Json->GetStringField(TEXT("refresh_token"));
		const int64 ExpSec = Json->GetIntegerField(TEXT("expires_in"));
		Token.ExpiresAtUtc = FDateTime::UtcNow() + FTimespan::FromSeconds(ExpSec);
		SaveTokenToConfig();

		UE_LOG(LogTemp, Log, TEXT("Token refreshed OK, expires at %s"), *Token.ExpiresAtUtc.ToString());

		FDruidsAuthResult R;
		R.Status  = EDruidsAuthStatus::Success;
		R.IdToken = Token.AccessToken;
		Finish(R);
	}
}

void UDruidsAuthManager::BeginDestroy()
{
	StopServer();
	UObject::BeginDestroy();
}

bool UDruidsAuthManager::StartServerPickPort()
{
	StopServer();

	// Try a handful of ephemeral ports
	const uint16 Start = 53619 + FMath::RandRange(0, 127);
	for (int i = 0; i < 64; ++i)
	{
		const uint16 TryPort = Start + i;
		USimpleHttpLoopbackServer* Candidate = NewObject<USimpleHttpLoopbackServer>(this);
	    if (Candidate->Start(TryPort))
	    {
	      // Single-cast delegate; if you made it multicast use AddUObject/RemoveAll
	      Candidate->OnRequest = FOnLoopbackRequest::CreateUObject(this, &UDruidsAuthManager::OnLoopbackRequest);
	      Server = Candidate;   // UPROPERTY keeps it referenced; Outer=this ties lifetime
	      Port = TryPort;
	      return true;
	    }
	    // Didn’t bind; allow GC to reclaim this unstarted instance
	    Candidate->ConditionalBeginDestroy();
	}
	return false;
}

void UDruidsAuthManager::StopServer()
{
	if (Server)
	{
		Server->OnRequest.Unbind();
		Server->Stop();
		Server = nullptr;
	}
	Port = 0;
	
	if (TimeoutTickerHandle.IsValid())
	{
		FTSTicker::GetCoreTicker().RemoveTicker(TimeoutTickerHandle);
		TimeoutTickerHandle.Reset();
	}
}

FString UDruidsAuthManager::BuildRedirectUri() const
{
	return FString::Printf(TEXT("http://127.0.0.1:%d%s"), Port, *RedirectPath);
}

static bool IsSafeChar(TCHAR C)
{
	return FChar::IsAlnum(C) || C == TEXT('-') || C == TEXT('_') || C == TEXT('.') || C == TEXT('~');
}

FString UDruidsAuthManager::UrlEncode(const FString& In)
{
	FString Out;
	Out.Reserve(In.Len() * 3);
	for (TCHAR C : In)
	{
		if (IsSafeChar(C)) { Out.AppendChar(C); }
		else if (C == TEXT(' ')) { Out.AppendChar(TEXT('+')); }
		else { Out += FString::Printf(TEXT("%%%02X"), (uint8)C); }
	}
	return Out;
}

FString UDruidsAuthManager::RandNonce()
{
	return FGuid::NewGuid().ToString(EGuidFormats::Digits);
}

FString UDruidsAuthManager::BuildLoginAuthUrl() const
{
	const FString LoginURL = BaseUrl + LoginPath;
	const bool bHasQuery = LoginURL.Contains(TEXT("?"));
	return LoginURL
		+ (bHasQuery ? TEXT("&") : TEXT("?"))
		+ TEXT("redirect_uri=") + UrlEncode(BuildRedirectUri())
		+ TEXT("&state=") + UrlEncode(StateNonce)
		+ TEXT("&client=ue");
}

void UDruidsAuthManager::OnLoopbackRequest(const FString& PathWithQuery, const TMap<FString, FString>& Query)
{
	UE_LOG(LogTemp, Log, TEXT("DruidsAuthManager: OnLoopbackRequest: %s"), *PathWithQuery);
	// Expect GET /callback?status=...&id_token=...&message=...&state=...
	FDruidsAuthResult R;

	const FString* StatusPtr = Query.Find(TEXT("status"));
	const FString* MessagePtr = Query.Find(TEXT("message"));
	const FString* IdTokenPtr = Query.Find(TEXT("id_token"));
	const FString* StatePtr = Query.Find(TEXT("state"));
	const FString* ExpStr = Query.Find(TEXT("expires_in"));
	const FString* RefreshTokenPtr = Query.Find(TEXT("refresh_token"));
	
	
	if (!StatePtr || *StatePtr != StateNonce)
	{
		R.Status  = EDruidsAuthStatus::Failed;
		R.Message = TEXT("State validation failed");
		Finish(R);
		return;
	}

	const FString StatusStr = StatusPtr ? StatusPtr->ToLower() : TEXT("");

	if (StatusStr == TEXT("success"))
	{

		int64 ExpSec = 0;
		if (ExpStr)
		{
			ExpSec = FCString::Strtoi64(**ExpStr, /*EndPtr=*/nullptr, /*Base=*/10);
		}
		Token.AccessToken  = IdTokenPtr ? *IdTokenPtr : FString();
		Token.RefreshToken = RefreshTokenPtr? *RefreshTokenPtr : FString();   // server must add this field
		Token.ExpiresAtUtc = FDateTime::UtcNow() + FTimespan::FromSeconds(ExpSec);
		SaveTokenToConfig();
		
		R.Status  = EDruidsAuthStatus::Success;
		R.IdToken = IdTokenPtr ? *IdTokenPtr : FString();
		R.Message = MessagePtr ? *MessagePtr : FString();
	}
	else if (StatusStr == TEXT("canceled"))
	{
		R.Status  = EDruidsAuthStatus::Canceled;
		R.Message = MessagePtr ? *MessagePtr : TEXT("Login canceled");
	}
	else
	{
		R.Status  = EDruidsAuthStatus::Failed;
		R.Message = MessagePtr ? *MessagePtr : TEXT("Login failed");
	}

	Finish(R);
}

//STORE TOKENS TO CONFIG HELPERS
void UDruidsAuthManager::LoadTokenFromConfig()
{
	Token = FDruidsTokenInfo{};
	
	GConfig->GetString(TEXT("/Script/Druids.Sage"), TEXT("AccessToken"), Token.AccessToken, GEditorPerProjectIni);
	GConfig->GetString(TEXT("/Script/Druids.Sage"), TEXT("RefreshToken"), Token.RefreshToken, GEditorPerProjectIni);
	FString ExpiryIso; 
	GConfig->GetString(TEXT("/Script/Druids.Sage"), TEXT("ExpiresAt"), ExpiryIso, GEditorPerProjectIni);
	FDateTime::ParseIso8601(*ExpiryIso, Token.ExpiresAtUtc);
}

void UDruidsAuthManager::SaveTokenToConfig() const
{
	GConfig->SetString(TEXT("/Script/Druids.Sage"), TEXT("AccessToken"),  *Token.AccessToken,  GEditorPerProjectIni);
	GConfig->SetString(TEXT("/Script/Druids.Sage"), TEXT("RefreshToken"), *Token.RefreshToken, GEditorPerProjectIni);
	GConfig->SetString(TEXT("/Script/Druids.Sage"), TEXT("ExpiresAt"),   *Token.ExpiresAtUtc.ToIso8601(), GEditorPerProjectIni);
	GConfig->Flush(false, GGameIni);
}

void UDruidsAuthManager::ClearTokenConfig()
{
	GConfig->RemoveKey(TEXT("/Script/Druids.Sage"), TEXT("AccessToken"),  GEditorPerProjectIni);
	GConfig->RemoveKey(TEXT("/Script/Druids.Sage"), TEXT("RefreshToken"), GEditorPerProjectIni);
	GConfig->RemoveKey(TEXT("/Script/Druids.Sage"), TEXT("ExpiresAt"),    GEditorPerProjectIni);
	GConfig->Flush(false, GGameIni);
}

void UDruidsAuthManager::Finish(const FDruidsAuthResult& Result)
{
	StopServer();      // stop + clear timeout
	StateNonce.Empty();

	if (Completion.IsBound())
	{
		Completion.Execute(Result);
	}
	Completion.Unbind();
}
