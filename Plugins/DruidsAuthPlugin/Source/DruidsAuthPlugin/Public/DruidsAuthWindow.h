#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "DruidsAuthManager.h"

class SDruidsAuthWindow : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SDruidsAuthWindow) {}
	SLATE_END_ARGS()

	
	/** Construct */
	void Construct(const FArguments& InArgs);

private:
	/* Auth callback */
	void HandleAuthComplete(const FDruidsAuthResult& Result);

	/* Helpers */
	void UpdateStatusLabel(const FString& InText, const FLinearColor& Color);
	FText GetLoginButtonText() const;
	FReply HandleLoginLogoutClicked();
	FReply OnDemoClicked();
	void HandleDemoAuthComplete(const FDruidsAuthResult& Result);

	/* UI refs */
	TSharedPtr<SButton> LoginButton;
	TSharedPtr<STextBlock> StatusLabel;
	TSharedPtr<STextBlock> TokenLabel;
};
