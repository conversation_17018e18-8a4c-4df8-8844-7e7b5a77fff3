#pragma once

#include <CoreMinimal.h>
#include "Blueprint/UserWidget.h"

#include "IDruidsSageChatItem.h"
#include "DruidsSageChatTypes.h"

// Forward declarations
class UDruidsSageMultiLineTextInput;
class UContextChipWidget;

class ISageExtensionDelegator;
class ISageCodeExecutor;
class ISageInvocationRequestFactory;
class ISuggestedPromptsHandler;
struct FDruidsSageExtensionDefinition;
class IChatRequestHandler;
struct FSageActionResult;
class UCheckBox;
class UButton;
class UScrollBox;
class UVerticalBox;
class UDruidsSageSuggestedPromptsChatItem;

class UDruidsSageChatRequest_v2;

#include "DruidsSageChatView.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnMessageSending);

/**
 * UMG Chat View widget for DruidsSage
 * Converted from SDruidsSageChatView Slate widget
 */
UCLASS(meta = (DisplayName = "Druids Sage Chat View"))
class SAGEUI_API UDruidsSageChatView : public UUserWidget
{
	GENERATED_BODY()

public:
	UDruidsSageChatView(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void NativeDestruct() override;
	virtual void SynchronizeProperties() override;
	virtual void ReleaseSlateResources(bool bReleaseChildren) override;
	// End of UUserWidget interface

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Chat View")
	FOnMessageSending OnMessageSending;

	// Public API
	UFUNCTION(BlueprintCallable, Category = "Chat View")
	bool IsSendMessageEnabled() const;

	UFUNCTION(BlueprintCallable, Category = "Chat View")
	bool IsClearChatEnabled() const;

	bool UseExtensions() const;
	bool UseInvocations() const;
	
	UFUNCTION(BlueprintCallable, Category = "Chat View")
	void ClearChat();

	// Clear all messaging handler references to prevent GC issues during level switches
	void ClearAllMessagingHandlerReferences();

	void SetTabContext(const FString& Context, const FString& ContextDisplayMessage);
	void SetBPContext(const FString& BPContext, const FString& BPContextDisplayMessage);
	void SetActiveObject(const TWeakObjectPtr<>& NewActiveObject);
	void SetActiveExtensionDefinitions(const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& NewActiveExtensions);
	void SetChatRequestHandler(const TWeakPtr<IChatRequestHandler>& NewChatRequestHandler);
	void SetInvocationRequestFactory(const TSharedPtr<class ISageInvocationRequestFactory>& NewInvocationRequestFactory);
	void SetExtensionsDelegator(const TWeakPtr<ISageExtensionDelegator>& NewExtensionDelegator);
	void SetCodeExecutor(const TWeakPtr<ISageCodeExecutor>& NewCodeExecutor);
	void SetSuggestedPromptsHandler(const TSharedPtr<ISuggestedPromptsHandler>& NewSuggestedPromptsHandler);

	// Focus management
	void SetInputFocus() const;

	// Send message programmatically
	UFUNCTION(BlueprintCallable, Category = "Chat View")
	void SendMessageProgrammatically(const FString& MessageText);

protected:
	// BindWidget properties for Blueprint binding
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UContextChipWidget* BPContextChipWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UContextChipWidget* TabContextChipWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UDruidsSageMultiLineTextInput* InputTextBox;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UCheckBox* UseExtensionsCheckbox;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UCheckBox* UseInvocationsCheckbox;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UButton* SendButton;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UButton* ClearButton;

	// UMG widgets for chat content
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, VisibleAnywhere, Category = "Chat View")
	UScrollBox* ChatScrollBox;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, VisibleAnywhere, Category = "Chat View")
	UVerticalBox* ChatBox;

private:
	TSharedPtr<IChatRequestHandler> ChatRequestHandler;

	// Chat functionality
	void HandleSendMessage(const EDruidsSageChatRole Role);
	void HandleClearChat();
	void NotifyAllAssistantChatItemsPromptMessageSent();

	UFUNCTION()
	void HandleEnterPressed();

	UFUNCTION()
	void HandleSendButtonClicked();

	UFUNCTION()
	void HandleClearButtonClicked();

	TArray<FDruidsSageChatMessage> GetChatHistory() const;
	void LoadChatHistory();
	void SaveChatHistory() const;
	static FString GetHistoryPath();

	// Thread ID management
	void SetThreadId(const FString& ThreadId);
	FString GetThreadId() const;
	void OnThreadIdReceived(const FString& ThreadId);

	// Tab/General Context
	FString CurrentTabContext;
	FString CurrentTabContextDisplayMessage;

	// Blueprint Context
	FString CurrentBPContext;
	FString CurrentBPContextDisplayMessage;

	// Thread ID for conversation continuity
	FString CurrentThreadId;

	TArray<UIDruidsSageChatItem*> ChatItems;

	TWeakObjectPtr<> ActiveObject;

	// Helper for truncation
	static FString TruncateString(const FString& Input, int32 MaxLength = 100);

	// Suggested prompts management
	void CheckAndShowSuggestedPrompts();
	void CreateSuggestedPromptsFromSaved();

	TArray<TSharedPtr<FDruidsSageExtensionDefinition>> ActiveExtensionDefinitions;
	TSharedPtr<ISageExtensionDelegator> ExtensionDelegator;
	TSharedPtr<ISageCodeExecutor> CodeExecutor;
	TSharedPtr<class ISageInvocationRequestFactory> InvocationRequestFactory;
	TSharedPtr<ISuggestedPromptsHandler> SuggestedPromptsHandler;

	class UIDruidsSageChatItem* CreateChatItem(
		EDruidsSageChatRole Role,
		const FString& ChatText = FString()) const;

	UFUNCTION()
	void OnActionRequestApplied(const FString& ActionDetailsJson, UDruidsSageActionRequestChatItem* ChatItem);

	UFUNCTION()
	void OnSuggestedPromptSelected(const FString& PromptText, UDruidsSageSuggestedPromptsChatItem* ChatItem);

	UFUNCTION()
	void OnChatResponseCompleted();
};
