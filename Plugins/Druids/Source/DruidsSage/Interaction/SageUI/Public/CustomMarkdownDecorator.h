#pragma once

#include "Framework/Text/TextDecorators.h"

class SAGEUI_API FCustomMarkdownDecorator final : public ITextDecorator
{
public:
	virtual bool Supports(const FTextRunParseResults& RunInfo, const FString& Text) const override;
	
	virtual TSharedRef<ISlateRun> Create(const TSharedRef<class FTextLayout>& TextLayout,
		const FTextRunParseResults& RunInfo, const FString& OriginalText, const TSharedRef<FString>& ModelText,
		const ISlateStyle* Style) override;
};
