#include "DruidsSageChatView.h"

#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonReader.h>
#include <Serialization/JsonSerializer.h>
#include <Misc/FileHelper.h>
#include <HAL/FileManager.h>

#include "SageActionTypes.h"
#include "ISageCodeExecutor.h"
#include "ISageInvocationRequestFactory.h"
#include "SageActionResultHandler.h"

#include <Widgets/Layout/SScrollBox.h>
#include <Framework/Application/SlateApplication.h>
#include <Input/Events.h>
#include <Engine/Engine.h>
#include <Engine/World.h>
#include "TimerManager.h"
#include <Components/PanelWidget.h>
#include <Components/Button.h>

#include "ChatItems/DruidsSageActionRequestChatItem.h"

#include "LogDruids.h"
#include "DruidsSageHelper.h"

#include "DruidsSageChatMemories.h"
#include "IChatRequestHandler.h"
#include "ChatItems/DruidsSageSimpleChatItem.h"
#include "ChatItems/DruidsSageAssistantChatItem.h"
#include "ISageExtensionDelegator.h"
#include "DruidsSageMultiLineTextInput.h"
#include "ContextChipWidget.h"
#include "Components/VerticalBox.h"
#include "Components/ScrollBox.h"
#include "SageUIModule.h"
#include "ChatWidgetOverrides.h"
#include "DruidsSageSuggestedPrompts.h"
#include "SageChatMessageHelper.h"
#include "ChatItems/DruidsSageSuggestedPromptsChatItem.h"
#include "Components/CheckBox.h"
#include "DruidsSageMessagingHandler.h"

UDruidsSageChatView::UDruidsSageChatView(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, BPContextChipWidget(nullptr)
	, TabContextChipWidget(nullptr)
	, InputTextBox(nullptr)
	, SendButton(nullptr)
	, ClearButton(nullptr)
	, ChatScrollBox()
	, ChatBox()
	, CurrentTabContext(TEXT(""))
	, CurrentTabContextDisplayMessage(TEXT(""))
	, CurrentBPContext(TEXT(""))
	, CurrentBPContextDisplayMessage(TEXT(""))
{
}

void UDruidsSageChatView::NativePreConstruct()
{
	Super::NativePreConstruct();
}

void UDruidsSageChatView::NativeConstruct()
{
	Super::NativeConstruct();

	if (IsDesignTime())
	{
		return;
	}

	// Initialize chat content area
	// Chat content is now handled by UMG widgets bound in Blueprint

	// Bind the Enter key event from the input text box
	if (InputTextBox)
	{
		InputTextBox->OnEnterPressed.AddDynamic(this, &UDruidsSageChatView::HandleEnterPressed);
	}

	// Bind button click events
	if (SendButton)
	{
		SendButton->OnClicked.AddDynamic(this, &UDruidsSageChatView::HandleSendButtonClicked);
	}

	if (ClearButton)
	{
		ClearButton->OnClicked.AddDynamic(this, &UDruidsSageChatView::HandleClearButtonClicked);
	}

	{
		FDruidsSageCommonOptions Options = UDruidsSageHelper::LoadCommonOptionsFromIni();
		if (UseExtensionsCheckbox)
		{
			UseExtensionsCheckbox->SetCheckedState(Options.UseExtensions ? ECheckBoxState::Checked : ECheckBoxState::Unchecked);
		}
		if (UseInvocationsCheckbox)
		{
			UseInvocationsCheckbox->SetCheckedState(Options.UseInvocations ? ECheckBoxState::Checked : ECheckBoxState::Unchecked);
		}
	}
	
	// Load chat history
	LoadChatHistory();

	// Set focus to the input text box after a short delay to ensure window is fully constructed
	if (GWorld)
	{
		GWorld->GetTimerManager().SetTimerForNextTick([this]()
		{
			SetInputFocus();
		});
	}
}

void UDruidsSageChatView::NativeDestruct()
{
	Super::NativeDestruct();

	if (IsDesignTime())
	{
		return;
	}

	SaveChatHistory();
	ClearChat();
}


void UDruidsSageChatView::SynchronizeProperties()
{
	Super::SynchronizeProperties();
}

void UDruidsSageChatView::ReleaseSlateResources(bool bReleaseChildren)
{
	Super::ReleaseSlateResources(bReleaseChildren);

	ChatItems.Empty();

	OnMessageSending.Clear();
}

bool UDruidsSageChatView::IsSendMessageEnabled() const
{
	const bool bNoActiveRequest = ChatRequestHandler.IsValid() && ChatRequestHandler.Get()->IsNoActiveRequest();
	return bNoActiveRequest && InputTextBox && !InputTextBox->IsEmpty();
}

bool UDruidsSageChatView::IsClearChatEnabled() const
{
	return !ChatItems.IsEmpty();
}

bool UDruidsSageChatView::UseExtensions() const
{
	if (UseExtensionsCheckbox)
	{
		return UseExtensionsCheckbox->GetCheckedState() == ECheckBoxState::Checked;
	}

	return true;
}

bool UDruidsSageChatView::UseInvocations() const
{
	if (UseInvocationsCheckbox)
	{
		return UseInvocationsCheckbox->GetCheckedState() == ECheckBoxState::Checked;
	}

	return false;
}

void UDruidsSageChatView::ClearChat()
{
	// First stop any ongoing request
	if (ChatRequestHandler.IsValid())
	{
		ChatRequestHandler.Get()->StopAndCleanupRequest(ChatItems);
	}

	// Clear all chat items
	ChatItems.Empty();

	// Clear the thread ID when clearing chat
	CurrentThreadId.Empty();

	// Clear the chat box if it exists
	if (ChatBox)
	{
		ChatBox->ClearChildren();
	}

	// Update context chip visibility
	if (BPContextChipWidget)
	{
		BPContextChipWidget->SetVisibility(CurrentBPContextDisplayMessage.IsEmpty() ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}

	// Check if we should show suggested prompts after clearing
	CheckAndShowSuggestedPrompts();
}

void UDruidsSageChatView::ClearAllMessagingHandlerReferences()
{
	// Clear scroll box references from all chat items to prevent GC issues
	for (UIDruidsSageChatItem* ChatItem : ChatItems)
	{
		if (ChatItem)
		{
			// Get the messaging handler and clear its scroll box reference
			TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler = ChatItem->GetMessagingHandler();
			if (MessagingHandler.IsValid())
			{
				MessagingHandler->ScrollBoxReference = nullptr;

				// Also call Destroy() to properly clean up the messaging handler
				MessagingHandler->Destroy();
			}
		}
	}
}

void UDruidsSageChatView::SetTabContext(const FString& Context, const FString& ContextDisplayMessage)
{
	CurrentTabContext = Context;
	CurrentTabContextDisplayMessage = ContextDisplayMessage;

	FString TruncatedMessage = TruncateString(ContextDisplayMessage);

	if (TabContextChipWidget)
	{
		TabContextChipWidget->SetChipText(TruncatedMessage);
		TabContextChipWidget->SetVisibility(TruncatedMessage.IsEmpty() ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void UDruidsSageChatView::SetBPContext(const FString& BPContext, const FString& BPContextDisplayMessage)
{
	CurrentBPContext = BPContext;
	CurrentBPContextDisplayMessage = BPContextDisplayMessage;

	FString TruncatedMessage = TruncateString(BPContextDisplayMessage);

	if (BPContextChipWidget)
	{
		BPContextChipWidget->SetChipText(TruncatedMessage);
		BPContextChipWidget->SetVisibility(TruncatedMessage.IsEmpty() ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void UDruidsSageChatView::SetActiveObject(const TWeakObjectPtr<>& NewActiveObject)
{
	ActiveObject = NewActiveObject;
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("Active Object Set: %s"), *GetNameSafe(NewActiveObject.Get()));
}

void UDruidsSageChatView::SetActiveExtensionDefinitions(
	const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& NewActiveExtensions)
{
	ActiveExtensionDefinitions = NewActiveExtensions;
}

void UDruidsSageChatView::SetChatRequestHandler(const TWeakPtr<IChatRequestHandler>& NewChatRequestHandler)
{
	ChatRequestHandler = NewChatRequestHandler.Pin().ToSharedRef();

	// Set up thread ID callback
	if (ChatRequestHandler.IsValid())
	{
		ChatRequestHandler.Get()->SetThreadIdCallback(FOnThreadIdReceived::CreateUObject(this, &UDruidsSageChatView::OnThreadIdReceived));
}
}

void UDruidsSageChatView::SetExtensionsDelegator(const TWeakPtr<ISageExtensionDelegator>& NewExtensionDelegator)
{
	ExtensionDelegator = NewExtensionDelegator.Pin();
}

void UDruidsSageChatView::SetCodeExecutor(const TWeakPtr<ISageCodeExecutor>& NewCodeExecutor)
{
	CodeExecutor = NewCodeExecutor.Pin();
}

void UDruidsSageChatView::SetInvocationRequestFactory(const TSharedPtr<ISageInvocationRequestFactory>& NewInvocationRequestFactory)
{
	InvocationRequestFactory = NewInvocationRequestFactory;
}

void UDruidsSageChatView::SetSuggestedPromptsHandler(const TSharedPtr<ISuggestedPromptsHandler>& NewSuggestedPromptsHandler)
{
	SuggestedPromptsHandler = NewSuggestedPromptsHandler;
}

void UDruidsSageChatView::SetInputFocus() const
{
	if (InputTextBox)
	{
		InputTextBox->SetInputFocus();
	}
}

void UDruidsSageChatView::SendMessageProgrammatically(const FString& MessageText)
{
	if (MessageText.IsEmpty())
	{
		return;
	}

	// Temporarily set the input text box to the message text
	FText OriginalText = InputTextBox ? InputTextBox->GetText() : FText::GetEmpty();
	if (InputTextBox)
	{
		InputTextBox->SetText(FText::FromString(MessageText));
	}

	// Send the message
	HandleSendMessage(EDruidsSageChatRole::User);

	// Restore the original text (which should be empty after sending)
	if (InputTextBox)
	{
		InputTextBox->SetText(OriginalText);
	}
}

void UDruidsSageChatView::HandleEnterPressed()
{
	if (IsSendMessageEnabled())
	{
		HandleSendMessage(EDruidsSageChatRole::User);
	}
}

void UDruidsSageChatView::HandleSendButtonClicked()
{
	if (IsSendMessageEnabled())
	{
		HandleSendMessage(EDruidsSageChatRole::User);
	}
}

void UDruidsSageChatView::HandleClearButtonClicked()
{
	if (IsClearChatEnabled())
	{
		HandleClearChat();
	}
}

FString UDruidsSageChatView::TruncateString(const FString& Input, int32 MaxLength /*= 100*/)
{
	if (Input.Len() > MaxLength)
	{
		return Input.Left(MaxLength - 3) + TEXT("...");
	}
	return Input;
}

UIDruidsSageChatItem* UDruidsSageChatView::CreateChatItem(
	EDruidsSageChatRole Role,
	const FString& ChatText) const
{
	if (Role == EDruidsSageChatRole::Assistant)
	{
		// Get the widget class from overrides
		TSubclassOf<UDruidsSageAssistantChatItem> WidgetClass = UDruidsSageAssistantChatItem::StaticClass();
		if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
		{
			if (UChatWidgetOverrides* Overrides = SageUIModule->GetChatWidgetOverrides())
			{
				if (TSubclassOf<UDruidsSageAssistantChatItem> OverrideClass = Overrides->GetAssistantChatItemWidgetClass())
				{
					WidgetClass = OverrideClass;
				}
			}
		}

		UDruidsSageAssistantChatItem* AssistantChatItem = CreateWidget<UDruidsSageAssistantChatItem>(GetWorld(), WidgetClass);
		if (AssistantChatItem)
		{
			AssistantChatItem->InitializeAssistantChatItem();
			AssistantChatItem->SetScrollBoxReference(ChatScrollBox);
			AssistantChatItem->OnActionApplied.AddDynamic(const_cast<UDruidsSageChatView*>(this), &UDruidsSageChatView::OnActionRequestApplied);
			AssistantChatItem->OnSuggestedPromptSelected.AddDynamic(const_cast<UDruidsSageChatView*>(this), &UDruidsSageChatView::OnSuggestedPromptSelected);
			AssistantChatItem->OnChatResponseCompleted.AddDynamic(const_cast<UDruidsSageChatView*>(this), &UDruidsSageChatView::OnChatResponseCompleted);

			// Inject the suggested prompts handler if available
			if (SuggestedPromptsHandler.IsValid())
			{
				AssistantChatItem->SetSuggestedPromptsHandler(SuggestedPromptsHandler);
			}

			AssistantChatItem->SetRawText(ChatText);
		}
		return AssistantChatItem;
	}

	// Get the widget class from overrides
	TSubclassOf<UDruidsSageSimpleChatItem> WidgetClass = UDruidsSageSimpleChatItem::StaticClass();
	if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
	{
		if (UChatWidgetOverrides* Overrides = SageUIModule->GetChatWidgetOverrides())
		{
			if (TSubclassOf<UDruidsSageSimpleChatItem> OverrideClass = Overrides->GetSimpleChatItemWidgetClass())
			{
				WidgetClass = OverrideClass;
			}
		}
	}

	UDruidsSageSimpleChatItem* SimpleChatItem = CreateWidget<UDruidsSageSimpleChatItem>(GetWorld(), WidgetClass);
	if (SimpleChatItem)
	{
		SimpleChatItem->InitializeSimpleChatItem(Role, ChatText);
		SimpleChatItem->SetScrollBoxReference(ChatScrollBox);
	}
	return SimpleChatItem;
}

void UDruidsSageChatView::OnActionRequestApplied(const FString& ActionDetailsJson, UDruidsSageActionRequestChatItem* ChatItem)
{
	// Convert string back to TSharedPtr<FJsonValue> for action processing
	TSharedPtr<FJsonValue> JsonValue;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ActionDetailsJson);
	if (FJsonSerializer::Deserialize(Reader, JsonValue))
	{
		// Determine action type by checking the JSON content
		bool bIsInvocationAction = false;
		TSharedPtr<FJsonObject>* JsonObject = nullptr;
		if (JsonValue.IsValid() && JsonValue->TryGetObject(JsonObject) && JsonObject)
		{
			// Check for "extension_id" field to identify extension actions
			FString ExtensionId;
			if ((*JsonObject)->TryGetStringField(TEXT("extension_id"), ExtensionId))
			{
				bIsInvocationAction = false;
				UE_LOG(LogTemp, Log, TEXT("DruidsSageChatView: Detected extension action with ID: %s"), *ExtensionId);
			}
			// Check for "prompt" field to identify invocation actions
			else if ((*JsonObject)->HasField(TEXT("prompt")))
			{
				bIsInvocationAction = true;
				UE_LOG(LogTemp, Log, TEXT("DruidsSageChatView: Detected invocation action with prompt"));
			}
			else
			{
				// Default to extension action if neither field is found
				bIsInvocationAction = false;
				UE_LOG(LogTemp, Warning, TEXT("DruidsSageChatView: Could not determine action type, defaulting to extension action"));
			}
		}

		FSageActionResult FinalResult;

		if (bIsInvocationAction)
		{
			// Call the code executor for invocation actions
			if (CodeExecutor.IsValid())
			{
				// Inject dependencies for async result handling
				// Inject the invocation request factory (if available)
				if (InvocationRequestFactory.IsValid())
				{
					CodeExecutor->SetInvocationRequestFactory(InvocationRequestFactory);
				}

				// Create and inject the action result handler
				TSharedPtr<FSageActionResultHandler> ResultHandler = MakeShared<FSageActionResultHandler>(ChatItem);
				CodeExecutor->SetActionResultHandler(ResultHandler);

				FinalResult = CodeExecutor.Get()->OnActionApplied(JsonValue);
			}
			else
			{
				FinalResult = FSageActionResult::NoAction();
				UE_LOG(LogTemp, Warning, TEXT("DruidsSageChatView: CodeExecutor not available for invocation action"));
			}
		}
		else
		{
			// Call the extension delegator for extension actions
			if (ExtensionDelegator.IsValid())
			{
				FinalResult = ExtensionDelegator.Get()->OnActionApplied(JsonValue);
			}
			else
			{
				FinalResult = FSageActionResult::NoAction();
				UE_LOG(LogTemp, Warning, TEXT("DruidsSageChatView: ExtensionDelegator not available for extension action"));
			}
		}

		// Only call HandleActionResult immediately for non-async actions
		// Async actions will call HandleActionResult themselves when they complete
		if (ChatItem && !FinalResult.bIsAsync)
		{
			ChatItem->HandleActionResult(FinalResult.bActionCalled, FinalResult.bSuccess, FinalResult.Result, FinalResult.ErrorMessage);
		}
		else if (ChatItem && FinalResult.bIsAsync)
		{
			// For async actions, the state management is now handled by the action request chat item
			// No need to manually disable the button here
			UE_LOG(LogTemp, Log, TEXT("DruidsSageChatView: Async action started, state management handled by chat item"));
		}
	}
	else
	{
		// JSON parsing failed
		FSageActionResult FailureResult(false, false, FString(), TEXT("Failed to parse action details JSON"));
		if (ChatItem)
		{
			ChatItem->HandleActionResult(FailureResult.bActionCalled, FailureResult.bSuccess, FailureResult.Result, FailureResult.ErrorMessage);
		}
	}
}

void UDruidsSageChatView::OnSuggestedPromptSelected(const FString& PromptText, UDruidsSageSuggestedPromptsChatItem* ChatItem)
{
	// Note: The SuggestedPromptsChatItem already calls MarkPromptAsUsed in its button click handlers
	// so we don't need to do it here - the prompt will be automatically removed from storage

	// Send the selected prompt as a user message
	SendMessageProgrammatically(PromptText);
}

void UDruidsSageChatView::HandleSendMessage(const EDruidsSageChatRole Role)
{
	// Broadcast that we're about to send a message
	OnMessageSending.Broadcast();

	// Notify all assistant chat items that a prompt message is being sent
	NotifyAllAssistantChatItemsPromptMessageSent();

	FString ChatText = InputTextBox ? InputTextBox->GetText().ToString() : FString();
	UIDruidsSageChatItem* NewUserChatItem = CreateChatItem(Role, ChatText);

	// Add to chat box if available
	if (ChatBox && NewUserChatItem)
	{
		ChatBox->AddChild(NewUserChatItem);
	}
	if (NewUserChatItem)
	{
		ChatItems.Add(NewUserChatItem);
	}

	if (Role == EDruidsSageChatRole::System)
	{
		if (ChatScrollBox)
		{
			ChatScrollBox->ScrollToEnd();
		}
		if (InputTextBox)
		{
			InputTextBox->SetText(FText::GetEmpty());
		}
		return;
	}

	UIDruidsSageChatItem* AssistantMessage = CreateChatItem(
		EDruidsSageChatRole::Assistant,
		FString()
	);

	// *** Combine Contexts for the AI Request ***
	// Simple concatenation for now. Might need more sophisticated formatting later.
	FString CombinedContext = "";
	if (!CurrentTabContext.IsEmpty())
	{
		CombinedContext += CurrentTabContext;
	}
	if (!CurrentBPContext.IsEmpty())
	{
		if (!CombinedContext.IsEmpty())
		{
			CombinedContext += "\n\n"; // Separator
		}
		CombinedContext += "Blueprint Context (current BP nodes selected):\n" + CurrentBPContext;
	}
	// ********************************************

	if (ChatRequestHandler.IsValid() && AssistantMessage)
	{
		bool UseExtensions = UseExtensionsCheckbox
			                     ? UseExtensionsCheckbox->GetCheckedState() == ECheckBoxState::Checked
			                     : true;
		bool UseInvocations = UseExtensionsCheckbox
			                      ? UseInvocationsCheckbox->GetCheckedState() == ECheckBoxState::Checked
			                      : true;

		ChatRequestHandler.Get()->SetupAndSendRequest(ChatItems, AssistantMessage, CombinedContext,
			                                          UseExtensions, UseInvocations, ActiveExtensionDefinitions, CurrentThreadId);
	}

	// Add to chat box if available
	if (ChatBox && AssistantMessage)
	{
		ChatBox->AddChild(AssistantMessage);
	}
	if (AssistantMessage)
	{
		ChatItems.Add(AssistantMessage);
	}

	if (ChatScrollBox)
	{
		ChatScrollBox->ScrollToEnd();
	}
	if (InputTextBox)
	{
		InputTextBox->SetText(FText::GetEmpty());
	}
}

void UDruidsSageChatView::HandleClearChat()
{
	ClearChat();
}

void UDruidsSageChatView::NotifyAllAssistantChatItemsPromptMessageSent()
{
	// Notify all chat items that a prompt message is being sent
	for (UIDruidsSageChatItem* ChatItem : ChatItems)
	{
		if (UDruidsSageAssistantChatItem* AssistantChatItem = Cast<UDruidsSageAssistantChatItem>(ChatItem))
		{
			AssistantChatItem->NotifyChildrenPromptMessageSent();
		}
		else if (UDruidsSageSuggestedPromptsChatItem* SuggestedPromptsItem = Cast<UDruidsSageSuggestedPromptsChatItem>(ChatItem))
		{
			// Also notify suggested prompts chat items to hide themselves
			SuggestedPromptsItem->OnPromptMessageSent();
		}
	}
}

TArray<FDruidsSageChatMessage> UDruidsSageChatView::GetChatHistory() const
{
	TArray<FDruidsSageChatMessage> Output;

	// Find the last assistant chat item to determine which one should include suggested prompts
	int32 LastAssistantChatItemIndex = -1;
	for (int32 i = ChatItems.Num() - 1; i >= 0; i--)
	{
		if (UDruidsSageAssistantChatItem* AssistantItem = Cast<UDruidsSageAssistantChatItem>(ChatItems[i]))
		{
			LastAssistantChatItemIndex = i;
			break;
		}
	}

	// Process each chat item
	for (int32 i = 0; i < ChatItems.Num(); i++)
	{
		UIDruidsSageChatItem* Item = ChatItems[i];
		if (Item)
		{
			// Set the flag for assistant chat items to indicate if they're the last one
			if (UDruidsSageAssistantChatItem* AssistantItem = Cast<UDruidsSageAssistantChatItem>(Item))
			{
				AssistantItem->SetIsLastAssistantChatItem(i == LastAssistantChatItemIndex);
			}

			if (UDruidsSageSuggestedPromptsChatItem* SuggestedPromptsItem = Cast<UDruidsSageSuggestedPromptsChatItem>(Item))
			{
				//Don't add a standalone suggested prompt item to the history
				continue;
			}
			
			FDruidsSageChatMessage DruidsMessage;
			Item->FillInDruidsMessage(DruidsMessage);
			Output.Add(DruidsMessage);
		}
	}

	return Output;
}

FString UDruidsSageChatView::GetHistoryPath()
{
	return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage"), TEXT("ChatHistory.json"));
}

void UDruidsSageChatView::LoadChatHistory()
{
	ChatItems.Empty();
	if (ChatBox)
	{
		ChatBox->ClearChildren();
	}

	// Clear thread ID when loading
	CurrentThreadId.Empty();

	SetTabContext(TEXT(""), TEXT(""));
	SetBPContext(TEXT(""), TEXT(""));

	if (const FString LoadPath = GetHistoryPath(); FPaths::FileExists(LoadPath))
	{
		FString FileContent;
		if (!FFileHelper::LoadFileToString(FileContent, *LoadPath))
		{
			return;
		}

		const TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);
		TSharedPtr<FJsonObject> JsonObject;

		if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
		{
			// Load thread ID if it exists
			FString LoadedThreadId;
			if (JsonObject->TryGetStringField(TEXT("ThreadId"), LoadedThreadId))
			{
				CurrentThreadId = LoadedThreadId;
			}

			const TArray<TSharedPtr<FJsonValue>>* DataArray;
			if (JsonObject->TryGetArrayField(TEXT("Data"), DataArray))
			{
				for (const TSharedPtr<FJsonValue>& MessageValue : *DataArray)
				{
					if (const TSharedPtr<FJsonObject> MessageItObj = MessageValue->AsObject())
					{
						// Create FDruidsSageChatMessage from JSON - same as when receiving new messages
						FDruidsSageChatMessage LoadedMessage;

						// Set role
						if (FString RoleString; MessageItObj->TryGetStringField(TEXT("role"), RoleString))
						{
							LoadedMessage.SetRole(UDruidsSageHelper::NameToRole(FName(*RoleString)));
						}

						// Set content array
						if (const TArray<TSharedPtr<FJsonValue>>* ContentArray; MessageItObj->TryGetArrayField(TEXT("content"), ContentArray))
						{
							LoadedMessage.SetContentArray(*ContentArray);
						}
						else if (FString ContentString; MessageItObj->TryGetStringField(TEXT("content"), ContentString))
						{
							// Handle legacy string content by converting to JSON array
							TArray<TSharedPtr<FJsonValue>> LegacyContentArray;
							TSharedPtr<FJsonObject> TextContent = MakeShared<FJsonObject>();
							TextContent->SetStringField("type", "text");
							TextContent->SetStringField("text", ContentString);
							LegacyContentArray.Add(MakeShared<FJsonValueObject>(TextContent));
							LoadedMessage.SetContentArray(LegacyContentArray);
						}

						// Set thinking array if present
						if (const TArray<TSharedPtr<FJsonValue>>* ThinkingArray; MessageItObj->TryGetArrayField(TEXT("thinking"), ThinkingArray))
						{
							LoadedMessage.SetThinkingArray(*ThinkingArray);
						}

						// Create chat item based on role and process it the same way as new messages
						UIDruidsSageChatItem* ChatItem = nullptr;

						if (LoadedMessage.GetRole() == EDruidsSageChatRole::Assistant)
						{
							// Create assistant chat item and update it from the message - same as new messages
							ChatItem = CreateChatItem(EDruidsSageChatRole::Assistant, TEXT(""));
							if (UDruidsSageAssistantChatItem* AssistantItem = Cast<UDruidsSageAssistantChatItem>(ChatItem))
							{
								AssistantItem->UpdateFromDruidsSageMessage(&LoadedMessage);
							}
						}
						else
						{
							// For user/system messages, extract text content for simple chat item
							FString TextContent;
							const TArray<TSharedPtr<FJsonValue>>& ContentArray = LoadedMessage.GetContentArray();
							for (const TSharedPtr<FJsonValue>& ContentValue : ContentArray)
							{
								TSharedPtr<FJsonObject>* ContentObj = nullptr;
								if (ContentValue.IsValid() && ContentValue->TryGetObject(ContentObj) && ContentObj)
								{
									FString ContentType;
									if ((*ContentObj)->TryGetStringField(TEXT("type"), ContentType) && ContentType == TEXT("text"))
									{
										(*ContentObj)->TryGetStringField(TEXT("text"), TextContent);
										break; // Use first text content found
									}
								}
							}

							ChatItem = CreateChatItem(LoadedMessage.GetRole(), TextContent);
						}

						if (ChatItem)
						{
							ChatItems.Add(ChatItem);
						}
					}
				}
			}
		}
	}

	// Add loaded items to the chat box
	if (ChatBox)
	{
		for (UIDruidsSageChatItem* Item : ChatItems)
		{
			if (Item)
			{
				ChatBox->AddChild(Item);
			}
		}
	}

	// Check if we should show suggested prompts after loading history
	CheckAndShowSuggestedPrompts();
}

void UDruidsSageChatView::SaveChatHistory() const
{
	const TSharedPtr<FJsonObject> JsonRequest = MakeShared<FJsonObject>();

	TArray<TSharedPtr<FJsonValue>> Data;
	for (const FDruidsSageChatMessage& Item : GetChatHistory())
	{
		Data.Add(FSageChatMessageHelper::GetMessageJson(Item, false));
	}

	if (!Data.IsEmpty())
	{
		JsonRequest->SetArrayField("Data", Data);
	}

	// Save thread ID if it exists
	if (!CurrentThreadId.IsEmpty())
	{
		JsonRequest->SetStringField("ThreadId", CurrentThreadId);
	}

	FString RequestContentString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestContentString);

	if (FJsonSerializer::Serialize(JsonRequest.ToSharedRef(), Writer))
	{
		// Ensure the directory exists
		const FString DirectoryPath = FPaths::GetPath(GetHistoryPath());
		if (!FPaths::DirectoryExists(DirectoryPath))
		{
			IFileManager::Get().MakeDirectory(*DirectoryPath, true);
		}

		FFileHelper::SaveStringToFile(RequestContentString, *GetHistoryPath());
	}
}

void UDruidsSageChatView::SetThreadId(const FString& ThreadId)
{
	CurrentThreadId = ThreadId;
}

FString UDruidsSageChatView::GetThreadId() const
{
	return CurrentThreadId;
}

void UDruidsSageChatView::OnThreadIdReceived(const FString& ThreadId)
{
	CurrentThreadId = ThreadId;
	// Save the chat history with the new thread ID
	SaveChatHistory();
}

void UDruidsSageChatView::OnChatResponseCompleted()
{
	// Find the last user request and assistant response for logging
	FString UserRequest;
	FString AssistantResponse;

	// Look for the most recent user and assistant chat items
	for (int32 i = ChatItems.Num() - 1; i >= 0; i--)
	{
		UIDruidsSageChatItem* ChatItem = ChatItems[i];
		if (!ChatItem)
		{
			continue;
		}

		if (ChatItem->GetMessageRole() == EDruidsSageChatRole::Assistant && AssistantResponse.IsEmpty())
		{
			AssistantResponse = ChatItem->GetPlainText();
		}
		else if (ChatItem->GetMessageRole() == EDruidsSageChatRole::User && UserRequest.IsEmpty())
		{
			UserRequest = ChatItem->GetPlainText();
		}

		// Stop once we have both
		if (!UserRequest.IsEmpty() && !AssistantResponse.IsEmpty())
		{
			break;
		}
	}

	// Log the exchange if we have both parts
	if (!UserRequest.IsEmpty() && !AssistantResponse.IsEmpty())
	{
		DruidsSageChatMemories::LogChatExchange(UserRequest, AssistantResponse);
		}
	}

void UDruidsSageChatView::CheckAndShowSuggestedPrompts()
{
	// Only show suggested prompts if chat is empty or has no conversation history
	if (!ChatItems.IsEmpty())
	{
		return;
	}

	CreateSuggestedPromptsFromSaved();
}

void UDruidsSageChatView::CreateSuggestedPromptsFromSaved()
{
	if (!SuggestedPromptsHandler.IsValid())
	{
		return;
	}

	// Get random prompts from saved ones
	TArray<FSuggestedPromptEntry> RandomPrompts;
	DruidsSageSuggestedPrompts::GetRandomPrompts(RandomPrompts);

	if (RandomPrompts.Num() == 0)
	{
		return; // No saved prompts available
	}

	// Get the widget class from overrides
	TSubclassOf<UDruidsSageSuggestedPromptsChatItem> WidgetClass = UDruidsSageSuggestedPromptsChatItem::StaticClass();
	if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
	{
		if (UChatWidgetOverrides* Overrides = SageUIModule->GetChatWidgetOverrides())
		{
			if (TSubclassOf<UDruidsSageSuggestedPromptsChatItem> OverrideClass = Overrides->GetSuggestedPromptsChatItemWidgetClass())
			{
				WidgetClass = OverrideClass;
			}
		}
	}

	// Create the suggested prompts chat item
	UDruidsSageSuggestedPromptsChatItem* SuggestedPromptsChatItem = CreateWidget<UDruidsSageSuggestedPromptsChatItem>(GetWorld(), WidgetClass);
	if (SuggestedPromptsChatItem)
	{
		// Set up the chat item
		SuggestedPromptsChatItem->SetScrollBoxReference(ChatScrollBox);
		SuggestedPromptsChatItem->SetSuggestedPromptsHandler(SuggestedPromptsHandler);
		SuggestedPromptsChatItem->OnSuggestedPromptSelected.AddDynamic(this, &UDruidsSageChatView::OnSuggestedPromptSelected);

		// Initialize with random prompts
		FString Summary1, Prompt1, Summary2, Prompt2;
		if (RandomPrompts.Num() > 0)
		{
			Summary1 = RandomPrompts[0].Summary;
			Prompt1 = RandomPrompts[0].Prompt;
		}
		if (RandomPrompts.Num() > 1)
		{
			Summary2 = RandomPrompts[1].Summary;
			Prompt2 = RandomPrompts[1].Prompt;
		}

		SuggestedPromptsChatItem->InitializeWithPrompts(Summary1, Prompt1, Summary2, Prompt2);

		// Add to chat
		if (ChatBox)
		{
			ChatBox->AddChild(SuggestedPromptsChatItem);
		}
		ChatItems.Add(SuggestedPromptsChatItem);

		// Scroll to show the prompts
		if (ChatScrollBox)
		{
			ChatScrollBox->ScrollToEnd();
		}
	}
}


