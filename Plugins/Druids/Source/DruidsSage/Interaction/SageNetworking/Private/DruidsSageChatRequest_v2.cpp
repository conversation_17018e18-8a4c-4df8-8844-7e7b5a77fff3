#include "DruidsSageChatRequest_v2.h"

#include <Interfaces/IHttpRequest.h>
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonReader.h>
#include <Serialization/JsonSerializer.h>
#include <Misc/ScopeTryLock.h>
#include <Async/Async.h>
#include <HttpModule.h>

#include "LogDruids.h"

#include "DruidsSageHelper.h"

#include "ISageExtensionDelegator.h"
#include "LocalizationDescriptor.h"
#include "SageChatMessageHelper.h"
#include "SageExtensionTypes.h"
#include "SimpleJSON.h"
#include "SagePythonWorker.h"
#include "SageSystemInfoGatherer.h"

#if WITH_EDITOR
#include <Editor.h>
#endif

#ifdef UE_INLINE_GENERATED_CPP_BY_NAME
#include UE_INLINE_GENERATED_CPP_BY_NAME(DruidsSageChatRequest_v2)
#endif

// Common endpoint URL for all Sage requests
FString SageCommonURL("https://groveserver.replit.app/sprout/caller/v1");

// Endpoint paths for different versions
FString SageEndpoint_v9("webhook/sage/v9");
FString SageEndpoint_v9a("webhook/sage/v9a");
FString SageEndpoint_v9b("webhook/sage/v9b");
FString SageEndpoint = SageEndpoint_v9b;

FString Environment_Staging("staging");
FString Environment_Production("production");
FString Environment = Environment_Production;

#if WITH_EDITOR
UDruidsSageChatRequest_v2* UDruidsSageChatRequest_v2::EditorTask(const TArray<FDruidsSageChatMessage>& Messages,
                                                                 const TSharedPtr<ISageExtensionDelegator>& ExtensionDelegator,
                                                                 const FString& UserFocusContext,
                                                                 const bool UseExtensions, const bool UseInvocations,
                                                                 const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& ActiveExtensionDefinitions,
                                                                 const FString& ThreadId)
{
	UDruidsSageChatRequest_v2* const NewAsyncTask = SendMessages(
		GEditor->GetEditorWorldContext().World(), Messages, ExtensionDelegator, UserFocusContext,
		UseExtensions, UseInvocations, ActiveExtensionDefinitions, ThreadId);

	NewAsyncTask->bIsEditorTask = true;

	return NewAsyncTask;
}
#endif

UDruidsSageChatRequest_v2* UDruidsSageChatRequest_v2::SendMessages(
	const UObject* const WorldContextObject,
	const TArray<FDruidsSageChatMessage>& Messages,
	const TSharedPtr<ISageExtensionDelegator>& ExtensionDelegator,
	const FString& UserFocusContext,
	const bool UseExtensions, const bool UseInvocations,
	TArray<TSharedPtr<FDruidsSageExtensionDefinition>> ActiveExtensionDefinitions,
	const FString& ThreadId)
{
	UDruidsSageChatRequest_v2* const NewAsyncTask = NewObject<UDruidsSageChatRequest_v2>();
	NewAsyncTask->Messages = Messages;
	NewAsyncTask->ExtensionDelegator = ExtensionDelegator;
	NewAsyncTask->UserFocusContext = UserFocusContext;
	NewAsyncTask->ThreadId = ThreadId;
	NewAsyncTask->UseExtensions = UseExtensions;
	NewAsyncTask->UseInvocations = UseInvocations;
	NewAsyncTask->ActiveExtensionDefinitions = ActiveExtensionDefinitions;
	NewAsyncTask->bThreadIdBroadcasted = false;


	NewAsyncTask->RegisterWithGameInstance(WorldContextObject);

	return NewAsyncTask;
}

bool UDruidsSageChatRequest_v2::CanActivateTask()
{
	if (!Super::CanActivateTask())
	{
		return false;
	}

	if (Messages.IsEmpty())
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Can't activate task: Invalid Messages."), *FString(__FUNCTION__), GetUniqueID());
		return false;
	}

	return true;
}

bool UDruidsSageChatRequest_v2::CanBindProgress() const
{
	//Always streaming
	return true;
}

FString UDruidsSageChatRequest_v2::GetEndpointURL() const
{
	return SageCommonURL;
}

void UDruidsSageChatRequest_v2::CustomizeRequestHeaders()
{
	Super::CustomizeRequestHeaders();

	if (HttpRequest.IsValid())
	{
		HttpRequest.Get()->SetHeader(TEXT("environment"), Environment);
	}
}

FString UDruidsSageChatRequest_v2::SetRequestContent()
{
	FScopeLock Lock(&Mutex);

	if (!HttpRequest.IsValid())
	{
		return FString();
	}

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Mounting content"), *FString(__FUNCTION__), GetUniqueID());

	// Create the main request object with endpoint and payload structure
	const TSharedPtr<FJsonObject> JsonRequest = MakeShared<FJsonObject>();

	// Set the endpoint path
	JsonRequest->SetStringField("endpoint", SageEndpoint);

	// Create the payload object that contains all the original request data
	const TSharedPtr<FJsonObject> PayloadObject = MakeShared<FJsonObject>();

	// Only include thread_id if we have one
	if (!ThreadId.IsEmpty())
	{
		PayloadObject->SetStringField("thread_id", ThreadId);
	}

	PayloadObject->SetBoolField("extensions", UseExtensions);
	PayloadObject->SetBoolField("invocations", UseInvocations);

	// Add system information
	TSharedPtr<FJsonObject> SystemInfo = FSageSystemInfoGatherer::GatherSystemInfo();
	if (SystemInfo.IsValid())
	{
		PayloadObject->SetObjectField("system_info", SystemInfo);
	}

	if (UserFocusContext.Len() > 0)
	{
		PayloadObject->SetStringField("user_focus_context", UserFocusContext);
	}

	TArray<TSharedPtr<FJsonValue>> MessagesJson;
	for (const FDruidsSageChatMessage& Iterator : Messages)
	{
		if (Iterator.GetRole() == EDruidsSageChatRole::SuggestedPrompts)
		{
			continue;
		}

		TSharedPtr<FJsonValue> MessageJSON = FSageChatMessageHelper::GetMessageJson(Iterator, true);
		if (UseExtensions && Iterator.GetRole() == EDruidsSageChatRole::User)
		{
			SimpleJSON MessagesSimpleJSON(MessageJSON);
			for (const TSharedPtr<FDruidsSageExtensionDefinition>& Extension : ActiveExtensionDefinitions)
			{
				if (Extension.IsValid())
				{
					int32 currentIndex = MessagesSimpleJSON["content"].AsNativeArray().Num();
					MessagesSimpleJSON["content"][currentIndex]["type"] = TEXT("extension");
					MessagesSimpleJSON["content"][currentIndex]["extension"] = Extension->GetExtensionDefinitionJson();
				}
			}
		}

		MessagesJson.Add(MessageJSON);
	}

	PayloadObject->SetArrayField("messages", MessagesJson);

	// Set the payload object in the main request
	JsonRequest->SetObjectField("payload", PayloadObject);

	FString RequestContentString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestContentString);
	FJsonSerializer::Serialize(JsonRequest.ToSharedRef(), Writer);

	return RequestContentString;
}

void UDruidsSageChatRequest_v2::OnProgressUpdated(const FString& Content, int32 BytesSent, int32 BytesReceived)
{
	FScopeLock Lock(&Mutex);

	if (Content.IsEmpty())
	{
		return;
	}

	TArray<FString> StreamedResponses = GetStreamedResponsesFromContent(Content);

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Progress Updated"), *FString(__FUNCTION__), GetUniqueID());
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Content: %s; Bytes Sent: %d; Bytes Received: %d"), *FString(__FUNCTION__), GetUniqueID(),
	       StreamedResponses.IsEmpty() ? TEXT("<none>") : *StreamedResponses.Top(), BytesSent, BytesReceived);

	ProcessStreamedResponses(StreamedResponses);

	if (!Response.bSuccess)
	{
		return;
	}

	if (!bInitialized)
	{
		bInitialized = true;

		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);
			ProgressStarted.Broadcast(Response);
		});
	}

	AsyncTask(ENamedThreads::GameThread, [this]
	{
		FScopeTryLock Lock(&Mutex);

		if (Lock.IsLocked())
		{
			ProgressUpdated.Broadcast(Response);
		}
	});
}

void UDruidsSageChatRequest_v2::OnProgressCompleted(const FString& Content, const bool bWasSuccessful)
{
	FScopeLock Lock(&Mutex);

	if (!bWasSuccessful || Content.IsEmpty())
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Request failed"), *FString(__FUNCTION__), GetUniqueID());
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			RequestFailed.Broadcast();
		});

		return;
	}

	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Process Completed"), *FString(__FUNCTION__), GetUniqueID());
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("%s (%d): Content: \nBEGIN>>>\n%s\n<<<END"), *FString(__FUNCTION__), GetUniqueID(), *Content);

	const TArray<FString> StreamedResponses = GetStreamedResponsesFromContent(Content);
	ProcessStreamedResponses(StreamedResponses);

	if (Response.bSuccess)
	{
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);

			ProcessCompleted.Broadcast(Response);
		});
	}
	else
	{
		UE_LOG(LogDruidsSage, Error, TEXT("%s (%d): Request failed"), *FString(__FUNCTION__), GetUniqueID());
		AsyncTask(ENamedThreads::GameThread, [this]
		{
			FScopeLock Lock(&Mutex);
			ErrorReceived.Broadcast(Response);
		});
	}
}

TArray<FString> UDruidsSageChatRequest_v2::GetStreamedResponsesFromContent(const FString& Content)
{
	TArray<FString> Deltas_In, Deltas_Out;
	Content.ParseIntoArray(Deltas_In, TEXT("\n\n"));
	for (FString Delta_In : Deltas_In)
	{
		if (Delta_In.StartsWith("data:"))
		{
			if (!Delta_In.StartsWith("data: [done]"))
			{
				Deltas_Out.Add(Delta_In.Replace(TEXT("data: "), TEXT("")));
			}
		}
	}
	
	return Deltas_Out;
}

void UDruidsSageChatRequest_v2::ProcessStreamedResponses(const TArray<FString>& StreamedResponses)
{
	FScopeLock Lock(&Mutex);

	Response.bSuccess = true;
	
	Response.Choices.Empty(StreamedResponses.Num());
	for (const FString& StreamedResponse : StreamedResponses)
	{
		ProcessStreamedResponse(StreamedResponse);
	}
}

void UDruidsSageChatRequest_v2::ProcessStreamedResponse(const FString& StreamedResponse)
{
	FScopeLock Lock(&Mutex);

	if (StreamedResponse.IsEmpty())
	{
		return;
	}

	SimpleJSON ResponseJSON(StreamedResponse);

	if (ResponseJSON["output"]["status"].AsString() == "success")
	{
		return;
	}
	
	if (ResponseJSON["choices"].IsEmpty())
	{
		if (TArray<TSharedPtr<FJsonValue>> QueryRequests = ResponseJSON["query_requests"].AsNativeArray(); !QueryRequests.IsEmpty())
		{
			ProcessQueryRequests(&QueryRequests);
			return;
		}

		if (TArray<TSharedPtr<FJsonValue>> PythonCompileRequests = ResponseJSON["python_compilation_requests"].AsNativeArray(); !PythonCompileRequests.IsEmpty())
		{
			ProcessPythonCompileRequests(&PythonCompileRequests);
			return;
		}

		if (const FString RequestType = ResponseJSON["type"].AsString(); !RequestType.IsEmpty() && ResponseJSON["payload"].IsValid())
		{
			TSharedPtr<FJsonObject> Payload = ResponseJSON["payload"].GetJsonObject();
			const FString ResponseThreadId = ResponseJSON["thread_id"].AsString();
			const FString RequestId = ResponseJSON["request_id"].AsString();
			const FString ResponseUrl = ResponseJSON["response_url"].AsString();
			ProcessGenericRequest(RequestType, Payload, ResponseThreadId, RequestId, ResponseUrl);
			return;
		}

		Response.bSuccess = false;
		return;
	}

	if (const FString ConversationThreadId = ResponseJSON["id"].AsString(); !ConversationThreadId.IsEmpty())
	{
		// Only broadcast the thread ID once to avoid multiple broadcasts during SSE updates
		if (!bThreadIdBroadcasted)
		{
			bThreadIdBroadcasted = true;
			// Broadcast the received thread ID so the chat view can save it
			AsyncTask(ENamedThreads::GameThread, [this, ConversationThreadId]
			{
				ThreadIdReceived.Broadcast(ConversationThreadId);
			});
		}
	}
	
	for (int i = 0; i < ResponseJSON["choices"].Size(); i++)
	{
		SimpleJSON ChoiceJSON = ResponseJSON["choices"][i];

		int32 ChoiceIndex = 0;
		if (ChoiceJSON.IsObject())
		{
			ChoiceIndex = static_cast<int32>(ChoiceJSON["index"]);
		}

		FDruidsSageChatChoice* Choice = Response.Choices.FindByPredicate([this, ChoiceIndex](const FDruidsSageChatChoice& Element)
		{
			return Element.Index == ChoiceIndex;
		});

		if (!Choice)
		{
			FDruidsSageChatChoice NewChoice;
			NewChoice.Index = ChoiceIndex;
			Choice = &Response.Choices.Add_GetRef(NewChoice);
		}

		if (ChoiceJSON.HasKey("message") && ChoiceJSON["message"].IsObject())
		{
			FString RoleString = static_cast<FString>(ChoiceJSON["message"]["role"]);
			Choice->Message.SetRole(RoleString == "user" ? EDruidsSageChatRole::User : EDruidsSageChatRole::Assistant);

			if (ChoiceJSON["message"].HasKey("thinking") && ChoiceJSON["message"]["thinking"].IsArray())
			{
				TArray<TSharedPtr<FJsonValue>> ThinkingArray = ChoiceJSON["message"]["thinking"].AsNativeArray();
				Choice->Message.SetThinkingArray(ThinkingArray);
			}
			
			if (ChoiceJSON["message"]["content"].IsString())
			{
				FString ContentString = static_cast<FString>(ChoiceJSON["message"]["content"]);
				Choice->Message.SetChatContent(ContentString);
			}
			else if (ChoiceJSON["message"]["content"].IsArray())
			{
				TArray<TSharedPtr<FJsonValue>> ContentArray = ChoiceJSON["message"]["content"].AsNativeArray(); 
				Choice->Message.SetContentArray(ContentArray);
			}
		}
		else if (ChoiceJSON.HasKey("delta") && ChoiceJSON["delta"].IsObject())
		{
			FString RoleString = static_cast<FString>(ChoiceJSON["delta"]["role"]);
			Choice->Message.SetRole(UDruidsSageHelper::NameToRole(*RoleString));

			FString ContentString = static_cast<FString>(ChoiceJSON["delta"]["content"]);
			Choice->Message.SetChatContent(Choice->Message.GetChatContent() + ContentString);
		}
		else if (ChoiceJSON.HasKey("text"))
		{
			FString MessageString = static_cast<FString>(ChoiceJSON["text"]);
			Choice->Message.SetRole(EDruidsSageChatRole::Assistant);
			Choice->Message.SetChatContent(Choice->Message.GetChatContent() + MessageString);
		}

		while (Choice->Message.GetChatContent().StartsWith("\n"))
		{
			Choice->Message.SetChatContent(Choice->Message.GetChatContent().RightChop(1));
		}

		if (ChoiceJSON.HasKey("finish_reason"))
		{
			Choice->FinishReason = *static_cast<FString>(ChoiceJSON["finish_reason"]);
		}
	}
}

void UDruidsSageChatRequest_v2::ProcessQueryRequests(const TArray<TSharedPtr<FJsonValue>>* QueryRequestsArray)
{
	FString QueryResponseUrl;
	TArray<TSharedPtr<FJsonObject>> QueryResponses;

	/**
	 * Call the pending query requests
	 */
	for (auto Iterator = QueryRequestsArray->CreateConstIterator(); Iterator; ++Iterator)
	{
		if (const TSharedPtr<FJsonObject> QueryRequestObj = (*Iterator)->AsObject(); QueryRequestObj.IsValid())
		{
			SimpleJSON QueryRequestJSON(*QueryRequestObj.Get());
			if (FString QueryRequestId = QueryRequestJSON["query_request_id"].AsString(); !QueryRequestId.IsEmpty())
			{
				if (!QueryResponsesDone.Contains(QueryRequestId))
				{
					TSharedPtr<FJsonValue> Results = ExtensionDelegator->OnQueryRequested(QueryRequestObj);

					SimpleJSON QueryResponse;
					QueryResponse["extension_id"] = QueryRequestJSON["extension_id"];
					QueryResponse["query_request_id"] = QueryRequestJSON["query_request_id"];
					QueryResponse["thread_id"] = QueryRequestJSON["thread_id"];
					QueryResponse["results"] = Results;
					
					QueryResponsesDone.Add(QueryRequestId);
					QueryResponses.Add(QueryResponse.GetJsonObject());
					
					if (FString ResponseUrlToUse; QueryResponseUrl.IsEmpty() && QueryRequestObj->TryGetStringField(TEXT("response_url"), ResponseUrlToUse))
					{
						QueryResponseUrl = ResponseUrlToUse;
					}
				}
			}
		}
	}

	/*
	 * Package up query responses and send back
	 */
	if (!QueryResponses.IsEmpty())
	{
		//
		// Set up the request
		//
		HttpRequest = FHttpModule::Get().CreateRequest();
		{
			HttpRequest->SetURL(QueryResponseUrl);
			HttpRequest->SetVerb("POST");
			HttpRequest->SetHeader("Content-Type", "application/json");
	
			// Use the DruidsUserToken as the value for the Authorization header
			FString AuthHeader = FString::Printf(TEXT("Bearer %s"), *CommonOptions.APIKey.ToString());
			HttpRequest->SetHeader("Authorization", AuthHeader);
		}

		//
		// Store the request while it is in flight
		//
		TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> StoredRequest;
		{
			FScopeLock Lock(&InFlightResponseMutex);

			// Store the request to keep it alive until completion
			StoredRequest = HttpRequest;
			InFlightResponses.Add(StoredRequest);
		}

		//
		// Add the query responses
		//
		{
			// Create a JSON object to hold the query responses
			TSharedPtr<FJsonObject> RequestBodyObj = MakeShared<FJsonObject>();
			
			// Create an array of query response objects
			TArray<TSharedPtr<FJsonValue>> QueryResponsesArray;
			for (const TSharedPtr<FJsonObject>& QueryResponse : QueryResponses)
			{
				QueryResponsesArray.Add(MakeShared<FJsonValueObject>(QueryResponse));
			}
			
			// Add the array to the JSON object
			RequestBodyObj->SetArrayField(TEXT("query_responses"), QueryResponsesArray);
			
			// Serialize the JSON object to a string
			FString RequestBodyString;
			TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBodyString);
			FJsonSerializer::Serialize(RequestBodyObj.ToSharedRef(), Writer);
			
			// Set the request content
			HttpRequest->SetContentAsString(RequestBodyString);
		}

		//
		// Fire the request off
		//
		{
			// Add completion handler to clean up the reference when done
			HttpRequest->OnProcessRequestComplete().BindLambda(
				[this, StoredRequest]
				(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bSuccess)
			{
				// Remove when complete
				FScopeLock Lock(&InFlightResponseMutex);
				InFlightResponses.Remove(StoredRequest);
			});

			AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]
			{
				HttpRequest->ProcessRequest();
			});
		}
	}
}

void UDruidsSageChatRequest_v2::ProcessPythonCompileRequests(const TArray<TSharedPtr<FJsonValue>>* PythonCompileRequestsArray)
{
	FString ResponseUrl;
	TArray<TSharedPtr<FJsonObject>> CompileResponses;

	/**
	 * Process the pending Python compile requests
	 */
	for (auto Iterator = PythonCompileRequestsArray->CreateConstIterator(); Iterator; ++Iterator)
	{
		if (const TSharedPtr<FJsonObject> CompileRequestObj = (*Iterator)->AsObject(); CompileRequestObj.IsValid())
		{
			if (FString RequestId; CompileRequestObj->TryGetStringField(TEXT("request_id"), RequestId))
			{
				if (!PythonCompileResponsesDone.Contains(RequestId))
				{
					// Extract Python code and optional GUID
					FString PythonCode;
					FString ExistingGuid;
					CompileRequestObj->TryGetStringField(TEXT("python_code"), PythonCode);
					CompileRequestObj->TryGetStringField(TEXT("existing_guid"), ExistingGuid);

					// Compile the Python code
					USagePythonWorker* Worker = USagePythonWorker::GetInstance();
					FSagePythonExecutionResult CompileResult = Worker->ExecuteAndStorePythonCode(PythonCode, ExistingGuid);

					// Create response object
					TSharedPtr<FJsonObject> ResponseObj = MakeShared<FJsonObject>();
					ResponseObj->SetStringField(TEXT("request_id"), RequestId);
					ResponseObj->SetStringField(TEXT("code_guid"), CompileResult.CodeGuid);
					ResponseObj->SetBoolField(TEXT("compile_success"), CompileResult.bCompileSuccess);
					ResponseObj->SetStringField(TEXT("results_message"), CompileResult.ResultMessage);

					// Add the response object to the original request for sending back
					CompileRequestObj->SetObjectField(TEXT("results"), ResponseObj);

					PythonCompileResponsesDone.Add(RequestId);
					CompileResponses.Add(CompileRequestObj);

					if (FString ResponseUrlToUse; ResponseUrl.IsEmpty() && CompileRequestObj->TryGetStringField(TEXT("response_url"), ResponseUrlToUse))
					{
						ResponseUrl = ResponseUrlToUse;
					}
				}
			}
		}
	}

	/*
	 * Package up compile responses and send back
	 */
	if (!CompileResponses.IsEmpty() && !ResponseUrl.IsEmpty())
	{
		SendPythonResponseToUrl(CompileResponses, ResponseUrl, TEXT("python_compilation_responses"));
	}
}

void UDruidsSageChatRequest_v2::SendPythonResponseToUrl(const TArray<TSharedPtr<FJsonObject>>& Responses, const FString& ResponseUrl, const FString& ResponseArrayKey)
{
	//
	// Set up the request
	//
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> PythonHttpRequest = FHttpModule::Get().CreateRequest();
	{
		PythonHttpRequest->SetURL(ResponseUrl);
		PythonHttpRequest->SetVerb("POST");
		PythonHttpRequest->SetHeader("Content-Type", "application/json");

		// Use the DruidsUserToken as the value for the Authorization header
		FString AuthHeader = FString::Printf(TEXT("Bearer %s"), *CommonOptions.APIKey.ToString());
		PythonHttpRequest->SetHeader("Authorization", AuthHeader);
	}

	//
	// Store the request while it is in flight
	//
	TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> StoredRequest;
	{
		FScopeLock Lock(&InFlightResponseMutex);

		// Store the request to keep it alive until completion
		StoredRequest = PythonHttpRequest;
		InFlightResponses.Add(StoredRequest);
	}

	//
	// Add the Python responses
	//
	{
		// Create a JSON object to hold the Python responses
		TSharedPtr<FJsonObject> RequestBodyObj = MakeShared<FJsonObject>();

		// Create an array of Python response objects
		TArray<TSharedPtr<FJsonValue>> ResponsesArray;
		for (const TSharedPtr<FJsonObject>& ResponseObj : Responses)
		{
			ResponsesArray.Add(MakeShared<FJsonValueObject>(ResponseObj));
		}

		// Add the array to the JSON object
		RequestBodyObj->SetArrayField(ResponseArrayKey, ResponsesArray);

		// Serialize the JSON object to a string
		FString RequestBodyString;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBodyString);
		FJsonSerializer::Serialize(RequestBodyObj.ToSharedRef(), Writer);

		// Set the request content
		PythonHttpRequest->SetContentAsString(RequestBodyString);
	}

	//
	// Fire the request off
	//
	{
		// Add completion handler to clean up the reference when done
		PythonHttpRequest->OnProcessRequestComplete().BindLambda(
			[this, StoredRequest]
			(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bSuccess)
		{
			// Remove when complete
			FScopeLock Lock(&InFlightResponseMutex);
			InFlightResponses.Remove(StoredRequest);
		});

		AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [PythonHttpRequest]
		{
			PythonHttpRequest->ProcessRequest();
		});
	}
}

void UDruidsSageChatRequest_v2::ProcessGenericRequest(const FString& RequestType,
                                                      const TSharedPtr<FJsonObject>& Payload, const FString& RequestThreadId,
                                                      const FString& RequestId, const FString& ResponseUrl)
{
	// Delegate to the utility class
	GenericRequestHandler.ProcessGenericRequest(RequestType, Payload, RequestThreadId, RequestId, ResponseUrl, CommonOptions);
}


