#include "DruidsSageEditorModule.h"

#include <ToolMenus.h>
#include <Widgets/Docking/SDockTab.h>

#include "Editor/Experimental/EditorInteractiveToolsFramework/Public/Behaviors/2DViewportBehaviorTargets.h"

#include "FBlueprintContextHandler.h"
#include "Framework/Application/SlateApplication.h"
#include "Misc/ConfigCacheIni.h"
#include "Interfaces/IMainFrameModule.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

#include "PropertyEditorModule.h"
#include "FTabContextHandler.h"

#include "LogDruids.h"

#include "DruidsSageStyle.h"
#include "DruidsSageChatView.h"
#include "DruidsSageHelper.h"

#include "Utils/DruidsSageClassDiscovery.h"

#include "SageExtensionDetails.h"
#include "SageExtension.h"
#include "ActiveSageExtensions.h"
#include "SageExtensionEditorOverrides.h"
#include "SageExtensionsModule.h"

#include "DruidsSageChatShell.h"

// Editor Utility Widget includes
#include "EditorUtilityWidgetBlueprint.h"
#include "EditorUtilitySubsystem.h"

// Tab management includes
#include "AssetRegistry/AssetRegistryModule.h"
#include "Framework/Docking/TabManager.h"
#include "Widgets/Docking/SDockTab.h"



#define LOCTEXT_NAMESPACE "FDruidsSageEditorModule"

void FDruidsSageEditorModule::StartupModule()
{
	// Initialize the style set
	FDruidsSageStyle::Initialize();
	FDruidsSageStyle::ReloadTextures();

	// Register this module as the editor interface
	FSageActiveObjectProvider::Set(this);
	
	BlueprintHandler = MakeShared<FBlueprintContextHandler>();
	BlueprintHandler->SetContextUpdateCallback([this]()
	{
		UpdateChatViewContext();
	});
	
	TabHandler = MakeShared<FTabContextHandler>();
	TabHandler->SetContextUpdateCallback([this]()
	{
		UpdateChatViewContext();
	});
	
	const FSimpleDelegate RegisterDelegate = FSimpleMulticastDelegate::FDelegate::CreateRaw(this, &FDruidsSageEditorModule::RegisterMenus);
	UToolMenus::RegisterStartupCallback(RegisterDelegate);

	RegisterMenus();

	FCoreDelegates::OnPostEngineInit.AddRaw(this, &FDruidsSageEditorModule::OnPostEngineInit);

	// Register detail customization
	FPropertyEditorModule& PropertyModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");
	PropertyModule.RegisterCustomClassLayout(
		USageExtension::StaticClass()->GetFName(),
		FOnGetDetailCustomizationInstance::CreateStatic(&FSageExtensionDetails::MakeInstance)
	);

	// Initialize the class discovery cache
	UDruidsSageClassDiscovery::Initialize();

	// SageMain module will handle the Blueprint context hookup
}

void FDruidsSageEditorModule::ShutdownModule()
{
	// SageMain module handles cleanup

	// Clear the Blueprint context check timer
	if (GEditor && BlueprintContextCheckTimer.IsValid())
	{
		GEditor->GetTimerManager()->ClearTimer(BlueprintContextCheckTimer);
		GEditor->GetTimerManager()->ClearTimer(BlueprintContextCheckTimer);
	}

	// Unregister world change delegates
	if (WorldCleanupDelegateHandle.IsValid())
	{
		FWorldDelegates::OnWorldCleanup.Remove(WorldCleanupDelegateHandle);
		WorldCleanupDelegateHandle.Reset();
	}
	if (PostWorldInitDelegateHandle.IsValid())
	{
		FWorldDelegates::OnPostWorldInitialization.Remove(PostWorldInitDelegateHandle);
		PostWorldInitDelegateHandle.Reset();
	}

	// Shutdown the style set
	FDruidsSageStyle::Shutdown();

	FSageActiveObjectProvider::Set(nullptr);
	
	if (BlueprintHandler.IsValid())
	{
		BlueprintHandler.Reset();
	}

	if (TabHandler.IsValid())
	{
		TabHandler.Reset();
	}
	
	ActiveChatView = nullptr;
	LastCreatedShell = nullptr;

	if (GEditor)
	{
		GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetOpenedInEditor().RemoveAll(this);
		GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetClosedInEditor().RemoveAll(this);

		GEditor->OnEditorClose().RemoveAll(this);
	}

	// Clean up the window if it exists
	if (ChatWindowWeakPtr.IsValid())
	{
		ChatWindowWeakPtr.Pin()->RequestDestroyWindow();
		ChatWindowWeakPtr.Reset();
	}

	UToolMenus::UnRegisterStartupCallback(this);
	UToolMenus::UnregisterOwner(this);

	FCoreDelegates::OnPreExit.RemoveAll(this);

	if (FModuleManager::Get().IsModuleLoaded("PropertyEditor"))
	{
		FPropertyEditorModule& PropertyModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");
		PropertyModule.UnregisterCustomClassLayout(USageExtension::StaticClass()->GetFName());
	}
}

void FDruidsSageEditorModule::OnPostEngineInit()
{
	if (GEditor)
	{
		GEditor->OnEditorClose().AddRaw(this, &FDruidsSageEditorModule::OnEditorClose);

		// Set up a timer to periodically check for Blueprint context changes
		FTimerDelegate TimerDelegate;
		TimerDelegate.BindRaw(this, &FDruidsSageEditorModule::CheckBlueprintContextChanges);
		GEditor->GetTimerManager()->SetTimer(BlueprintContextCheckTimer, TimerDelegate, 0.5f, true);

		// Register for world change events to detect level switches
		WorldCleanupDelegateHandle = FWorldDelegates::OnWorldCleanup.AddRaw(this, &FDruidsSageEditorModule::OnWorldCleanup);
		PostWorldInitDelegateHandle = FWorldDelegates::OnPostWorldInitialization.AddRaw(this, &FDruidsSageEditorModule::OnPostWorldInitialization);

		UE_LOG(LogDruidsSage, Log, TEXT("Level switch detection enabled - chat window will auto-close/reopen on level changes"));
	}
}

TSharedRef<SWindow> FDruidsSageEditorModule::CreateFloatingChatWindow()
{
	// Load saved options
	FDruidsSageCommonOptions Options = UDruidsSageHelper::LoadCommonOptionsFromIni();

	// Ensure minimum window size
	float WindowWidth = FMath::Max(Options.WindowWidth, Options.MinWindowWidth);
	float WindowHeight = FMath::Max(Options.WindowHeight, Options.MinWindowHeight);
	FVector2D InitialSize(WindowWidth, WindowHeight);

	// Validate and adjust window position
	FVector2D WindowPosition(Options.WindowPositionX, Options.WindowPositionY);

	// Check if we have a saved position (using sentinel values to indicate "not set")
	// Both X and Y must be -1.0f to indicate no saved position
	bool bHasSavedPosition = !(Options.WindowPositionX == -1.0f && Options.WindowPositionY == -1.0f);

	if (bHasSavedPosition)
	{
		// Check if the saved position is still on any available monitor
		if (!IsPositionOnValidMonitor(WindowPosition, InitialSize))
		{
			UE_LOG(LogDruidsSage, Warning, TEXT("Saved window position (%.1f, %.1f) is no longer on any available monitor. Using default position."),
				WindowPosition.X, WindowPosition.Y);
			WindowPosition = FVector2D(-1.0f, -1.0f); // Use default positioning
		}
		else
		{
			// Validate the position is fully on screen (adjust if partially off-screen)
			FVector2D OriginalPosition = WindowPosition;
			WindowPosition = ValidateWindowPosition(WindowPosition, InitialSize);

			if (OriginalPosition != WindowPosition)
			{
				UE_LOG(LogDruidsSage, Log, TEXT("Window position adjusted from (%.1f, %.1f) to (%.1f, %.1f) to ensure it's fully visible"),
					OriginalPosition.X, OriginalPosition.Y, WindowPosition.X, WindowPosition.Y);
			}
			else
			{
				UE_LOG(LogDruidsSage, Log, TEXT("Window position (%.1f, %.1f) is valid, no adjustment needed"), WindowPosition.X, WindowPosition.Y);
			}
		}
	}
	else
	{
		UE_LOG(LogDruidsSage, Log, TEXT("No saved window position found, using default positioning"));
	}

	// Create the window
	TSharedRef<SWindow> NewWindow = SNew(SWindow)
		.Title(FText::FromString(TEXT("Druids Sage Chat")))
		.ClientSize(InitialSize) // Use validated size
		.SupportsMaximize(true)         // Allow maximization
		.SupportsMinimize(false)          // Do not allow minimization
		.SizingRule(ESizingRule::UserSized) // Allow resizing
		.IsInitiallyMaximized(false)
		.HasCloseButton(true);

	// Set the content
	if (OnCreateChatShell.IsBound())
	{
		LastCreatedShell = OnCreateChatShell.Execute();
	}

	if (LastCreatedShell)
	{
		// Get the current view from the UMG widget
		ActiveChatView = LastCreatedShell->GetCurrentView();

		// Bind the message sending delegate using a lambda
		LastCreatedShell->OnMessageSending.BindRaw(this, &FDruidsSageEditorModule::UpdateActiveExtensions);

		// Use TakeWidget() to get the Slate widget from the UMG widget
		TSharedRef<SWidget> SlateWidget = LastCreatedShell->TakeWidget();
		NewWindow->SetContent(SlateWidget);
	}
	else
	{
		UE_LOG(LogDruidsSage_Internal, Error, TEXT("FDruidsSageEditorModule::CreateFloatingChatWindow() - ChatShell was not created"));
	}

	UpdateChatViewContext();

	NewWindow->SetOnWindowClosed(FOnWindowClosed::CreateRaw(this, &FDruidsSageEditorModule::OnWindowClosed));

	// Get the main editor window as parent to ensure proper window hierarchy
	IMainFrameModule& MainFrameModule = FModuleManager::LoadModuleChecked<IMainFrameModule>(TEXT("MainFrame"));
	TSharedPtr<SWindow> ParentWindow = MainFrameModule.GetParentWindow();

	if (ParentWindow.IsValid())
	{
		// Add as child window to ensure it minimizes with the editor
		FSlateApplication::Get().AddWindowAsNativeChild(NewWindow, ParentWindow.ToSharedRef());
	}
	else
	{
		// Fallback to regular window if parent not available
		FSlateApplication::Get().AddWindow(NewWindow);
	}

	// Set window position after adding to application (if we have a valid saved position)
	if (WindowPosition.X != -1.0f && WindowPosition.Y != -1.0f)
	{
		UE_LOG(LogDruidsSage, Log, TEXT("Moving window to saved position: (%.1f, %.1f)"), WindowPosition.X, WindowPosition.Y);
		NewWindow->MoveWindowTo(WindowPosition);
	}
	else
	{
		UE_LOG(LogDruidsSage, Log, TEXT("Using default window positioning"));
	}

	// Set focus to the input text box after window is fully shown
	if (ActiveChatView.IsValid() && GWorld)
	{
		FTimerHandle TimerHandle;
		GWorld->GetTimerManager().SetTimer(TimerHandle, [this]()
		{
			if (ActiveChatView.IsValid())
			{
				ActiveChatView.Get()->SetInputFocus();
			}
		}, 0.1f, false); // Small delay to ensure window is fully shown
	}

	return NewWindow;
}

void FDruidsSageEditorModule::SaveWindowSize(const FVector2D& NewSize)
{
	// Save the window size to config
	if (GConfig)
	{
		GConfig->SetVector2D(
			TEXT("DruidsSage.ChatWindow"),
			TEXT("Size"),
			NewSize,
			GEditorPerProjectIni
		);
		GConfig->Flush(false, GEditorPerProjectIni);
	}
}

void FDruidsSageEditorModule::SaveCurrentChatWindowSize() const
{
	if (ChatWindowWeakPtr.IsValid())
	{
		TSharedPtr<SWindow> Window = ChatWindowWeakPtr.Pin();
		if (Window.IsValid())
		{
			SaveWindowSize(Window->GetClientSizeInScreen());
		}
	}
}

void FDruidsSageEditorModule::OnWindowClosed(const TSharedRef<SWindow>& Window)
{
	SaveCurrentChatWindowState();

	if (ChatWindowWeakPtr.IsValid() && ChatWindowWeakPtr.Pin() == Window)
	{
		// Clear the weak pointer and reset timing
		ChatWindowWeakPtr.Reset();
	}

	LastCreatedShell = nullptr;
}

void FDruidsSageEditorModule::UpdateChatViewContext() const
{
	if (ActiveChatView.IsValid())
	{
		// Get Tab Context from TabHandler
		if (TabHandler.IsValid())
		{
			FString TabDisplayContext;
			FString TabFullContext;
			TWeakObjectPtr ActiveObject = TabHandler->RefreshTabContext(TabFullContext, TabDisplayContext);
			
			ActiveChatView.Get()->SetTabContext(TabFullContext, TabDisplayContext);
			ActiveChatView.Get()->SetActiveObject(ActiveObject);
		}
		
		// Get BP Context from BlueprintHandler
		if (BlueprintHandler.IsValid() && BlueprintHandler->IsBlueprintFocused())
		{
			FString BPFullContext;
			FString BPDisplayContext;
			BlueprintHandler->GetBlueprintContext(BPFullContext, BPDisplayContext);
			ActiveChatView.Get()->SetBPContext(BPFullContext, BPDisplayContext);
		}
		else
		{
			ActiveChatView.Get()->SetBPContext(TEXT(""), TEXT(""));
		}
	}
	else
	{
		UE_LOG(LogDruidsSage_Internal, Display, TEXT("UpdateChatViewContext called but ActiveChatView is not valid."));
	}
}

void FDruidsSageEditorModule::OnEditorClose()
{
	if (TabHandler.IsValid())
	{
		TabHandler->Cleanup();
	}
}

void FDruidsSageEditorModule::OnEnginePreExit() const
{
	SaveCurrentChatWindowState();
}

void FDruidsSageEditorModule::ToggleChatWindow()
{
	// Don't allow toggling during level switch to prevent issues
	if (bIsLevelSwitchInProgress)
	{
		UE_LOG(LogDruidsSage, Warning, TEXT("Cannot toggle chat window during level switch"));
		return;
	}

	// Check if the window already exists and is valid
	if (ChatWindowWeakPtr.IsValid() && ChatWindowWeakPtr.Pin()->IsVisible())
	{
		TSharedPtr<SWindow> ChatWindow = ChatWindowWeakPtr.Pin();

		LastCreatedShell = nullptr;
		ChatWindow->RequestDestroyWindow();
		ChatWindowWeakPtr.Reset();
		return;
	}

	// Window doesn't exist or isn't visible, so create/open it
	TSharedRef<SWindow> NewWindow = CreateFloatingChatWindow();
	ChatWindowWeakPtr = NewWindow; // Store the weak pointer
}

void FDruidsSageEditorModule::RegisterMenus()
{
	FToolMenuOwnerScoped OwnerScoped(this);

	// Add a menu entry under the "Tools" menu
	UToolMenu* MenuTool = UToolMenus::Get()->ExtendMenu("LevelEditor.MainMenu.Tools");
	FToolMenuSection& Section = MenuTool->AddSection("DruidsSageSection", LOCTEXT("DruidsSageSection", "DruidsSage"));
	Section.AddMenuEntry(
		"ToggleDruidsSageChat",
		LOCTEXT("DruidsSageChatLabel", "Druids Sage Chat"),
		LOCTEXT("DruidsSageChatTooltip", "Open/Close DruidsSage Chat Window (brings to front if behind other windows)"),
		FSlateIcon(FDruidsSageStyle::GetStyleSetName(), "DruidsSage.ChatIcon"),
		FUIAction(
			FExecuteAction::CreateRaw(this, &FDruidsSageEditorModule::ToggleChatWindow)
		)
	);

	// Add toolbar buttons to multiple editor toolbars
	RegisterToolbarButtons();
}

void FDruidsSageEditorModule::RegisterToolbarButtons()
{
	// List of toolbar menus to extend
	TArray<FString> ToolbarMenuNames = {
		// Level Editor
		"LevelEditor.LevelEditorToolBar.PlayToolBar",           // Level Editor Play toolbar

		// Blueprint-related Editors
		"AssetEditor.BlueprintEditor.ToolBar",                 // Blueprint Editor toolbar
		"AssetEditor.BlueprintStructEditor.ToolBar",           // Blueprint Struct Editor toolbar
		"AssetEditor.BlueprintEnumEditor.ToolBar",             // Blueprint Enum Editor toolbar
		"AssetEditor.BlueprintInterfaceEditor.ToolBar",        // Blueprint Interface Editor toolbar
		"AssetEditor.WidgetBlueprintEditor.ToolBar",           // Widget Blueprint Editor toolbar

		// Material and Texture Editors
		"AssetEditor.MaterialEditor.ToolBar",                  // Material Editor toolbar
		"AssetEditor.MaterialInstanceEditor.ToolBar",          // Material Instance Editor toolbar
		"AssetEditor.TextureEditor.ToolBar",                   // Texture Editor toolbar
		"AssetEditor.RenderTargetEditor.ToolBar",              // Render Target Editor toolbar

		// Mesh Editors
		"AssetEditor.StaticMeshEditor.ToolBar",                // Static Mesh Editor toolbar
		"AssetEditor.SkeletalMeshEditor.ToolBar",              // Skeletal Mesh Editor toolbar
		"AssetEditor.PhysicsAssetEditor.ToolBar",              // Physics Asset Editor toolbar

		// Animation Editors
		"AssetEditor.AnimationEditor.ToolBar",                 // Animation Editor toolbar
		"AssetEditor.AnimationBlueprintEditor.ToolBar",        // Animation Blueprint Editor toolbar
		"AssetEditor.ControlRigEditor.ToolBar",                // Control Rig Editor toolbar

		// Audio Editors
		"AssetEditor.SoundCueEditor.ToolBar",                  // Sound Cue Editor toolbar
		"AssetEditor.SoundWaveEditor.ToolBar",                 // Sound Wave Editor toolbar

		// Particle and VFX Editors
		"AssetEditor.ParticleSystemEditor.ToolBar",            // Particle System Editor toolbar
		"AssetEditor.NiagaraEditor.ToolBar",                   // Niagara Editor toolbar

		// AI Editors
		"AssetEditor.BehaviorTreeEditor.ToolBar",              // Behavior Tree Editor toolbar
		"AssetEditor.BlackboardEditor.ToolBar",                // Blackboard Editor toolbar

		// Data Editors
		"AssetEditor.DataTableEditor.ToolBar",                 // Data Table Editor toolbar
		"AssetEditor.CurveTableEditor.ToolBar",                // Curve Table Editor toolbar
		"AssetEditor.CurveEditor.ToolBar",                     // Curve Editor toolbar

		// Sequencer and Media
		"AssetEditor.LevelSequenceEditor.ToolBar",             // Level Sequence Editor toolbar
		"AssetEditor.MediaPlayerEditor.ToolBar",               // Media Player Editor toolbar

		// Font and UI
		"AssetEditor.FontEditor.ToolBar",                      // Font Editor toolbar
		"AssetEditor.SlateWidgetStyleEditor.ToolBar",          // Slate Widget Style Editor toolbar

		// Landscape and Foliage
		"AssetEditor.LandscapeEditor.ToolBar",                 // Landscape Editor toolbar
		"AssetEditor.FoliageTypeEditor.ToolBar"                // Foliage Type Editor toolbar
	};

	// Add toolbar button to each specified toolbar
	for (const FString& MenuName : ToolbarMenuNames)
	{
		AddToolbarButtonToMenu(MenuName);
	}

	// Add SageExtension editor button specifically to Blueprint Editor toolbar
	AddSageExtensionToolbarButton("AssetEditor.BlueprintEditor.ToolBar");
}

void FDruidsSageEditorModule::AddToolbarButtonToMenu(const FString& MenuName)
{
	UToolMenu* ToolbarMenu = UToolMenus::Get()->ExtendMenu(*MenuName);
	if (ToolbarMenu)
	{
		FToolMenuSection& ToolbarSection = ToolbarMenu->FindOrAddSection("DruidsSage");
		FToolMenuEntry& Entry = ToolbarSection.AddEntry(FToolMenuEntry::InitToolBarButton(
			"ToggleDruidsSageChat",
			FUIAction(
				FExecuteAction::CreateRaw(this, &FDruidsSageEditorModule::ToggleChatWindow)
			),
			LOCTEXT("DruidsSageChatLabel", "Druids Sage"),
			LOCTEXT("DruidsSageChatTooltip", "Open/Close DruidsSage Chat Window (brings to front if behind other windows)"),
			FSlateIcon(FDruidsSageStyle::GetStyleSetName(), "DruidsSage.ChatIcon")
		));

		UE_LOG(LogDruidsSage, Log, TEXT("Added DruidsSage toolbar button to: %s"), *MenuName);
	}
	else
	{
		UE_LOG(LogDruidsSage, Warning, TEXT("Failed to extend toolbar menu: %s"), *MenuName);
	}
}

TWeakObjectPtr<> FDruidsSageEditorModule::GetActiveObject() const
{
	return TabHandler.IsValid() ? TabHandler->GetActiveObject() : TWeakObjectPtr();
}

void FDruidsSageEditorModule::SaveCurrentChatWindowState() const
{
	if (ChatWindowWeakPtr.IsValid())
	{
		TSharedPtr<SWindow> Window = ChatWindowWeakPtr.Pin();
		if (Window.IsValid())
		{
			// Get current window state
			FVector2D WindowSize = Window->GetClientSizeInScreen();
			FVector2D WindowPosition = Window->GetPositionInScreen();
			FString MonitorName = GetCurrentMonitorName(WindowPosition);

			// Create options structure with current values
			FDruidsSageCommonOptions Options = UDruidsSageHelper::LoadCommonOptionsFromIni();
			Options.WindowWidth = WindowSize.X;
			Options.WindowHeight = WindowSize.Y;
			Options.WindowPositionX = WindowPosition.X;
			Options.WindowPositionY = WindowPosition.Y;

			if (ActiveChatView.IsValid())
			{
				Options.UseExtensions = ActiveChatView->UseExtensions();
				Options.UseInvocations = ActiveChatView->UseInvocations();
			}

			// Save to INI file
			UDruidsSageHelper::SaveWindowOptionsToIni(Options);
		}
	}
}

FVector2D FDruidsSageEditorModule::ValidateWindowPosition(const FVector2D& Position, const FVector2D& Size) const
{
	// Get the work area of all monitors
	FDisplayMetrics DisplayMetrics;
	FSlateApplication::Get().GetDisplayMetrics(DisplayMetrics);

	FVector2D ValidatedPosition = Position;

	// Check if the window would be completely off-screen
	bool bFoundValidMonitor = false;
	for (const FMonitorInfo& Monitor : DisplayMetrics.MonitorInfo)
	{
		const FPlatformRect& MonitorRect = Monitor.WorkArea;

		// Check if any part of the window would be visible on this monitor
		// Convert to simple bounds checking
		float WindowLeft = Position.X;
		float WindowTop = Position.Y;
		float WindowRight = Position.X + Size.X;
		float WindowBottom = Position.Y + Size.Y;

		// Check if rectangles intersect
		bool bIntersects = !(WindowRight < MonitorRect.Left ||
		                    WindowLeft > MonitorRect.Right ||
		                    WindowBottom < MonitorRect.Top ||
		                    WindowTop > MonitorRect.Bottom);

		if (bIntersects)
		{
			bFoundValidMonitor = true;

			// Ensure the window is not positioned off the edges of this monitor
			ValidatedPosition.X = FMath::Clamp(Position.X, (float)MonitorRect.Left, (float)MonitorRect.Right - Size.X);
			ValidatedPosition.Y = FMath::Clamp(Position.Y, (float)MonitorRect.Top, (float)MonitorRect.Bottom - Size.Y);
			break;
		}
	}

	// If no valid monitor found, use primary monitor
	if (!bFoundValidMonitor && DisplayMetrics.MonitorInfo.Num() > 0)
	{
		const FPlatformRect& PrimaryMonitor = DisplayMetrics.PrimaryDisplayWorkAreaRect;
		ValidatedPosition.X = (float)PrimaryMonitor.Left + 100; // Small offset from edge
		ValidatedPosition.Y = (float)PrimaryMonitor.Top + 100;
	}

	return ValidatedPosition;
}

FString FDruidsSageEditorModule::GetCurrentMonitorName(const FVector2D& WindowPosition) const
{
	FDisplayMetrics DisplayMetrics;
	FSlateApplication::Get().GetDisplayMetrics(DisplayMetrics);

	UE_LOG(LogDruidsSage, Log, TEXT("Getting monitor name for window position: (%.1f, %.1f)"), WindowPosition.X, WindowPosition.Y);

	// Find which monitor contains the window position
	for (int32 i = 0; i < DisplayMetrics.MonitorInfo.Num(); ++i)
	{
		const FMonitorInfo& Monitor = DisplayMetrics.MonitorInfo[i];
		const FPlatformRect& MonitorRect = Monitor.WorkArea;

		UE_LOG(LogDruidsSage, Log, TEXT("Checking monitor [%d] '%s': Left=%d, Top=%d, Right=%d, Bottom=%d"),
			i, *Monitor.Name, MonitorRect.Left, MonitorRect.Top, MonitorRect.Right, MonitorRect.Bottom);

		// Check if the point is within this monitor's bounds
		if (WindowPosition.X >= MonitorRect.Left && WindowPosition.X <= MonitorRect.Right &&
		    WindowPosition.Y >= MonitorRect.Top && WindowPosition.Y <= MonitorRect.Bottom)
		{
			UE_LOG(LogDruidsSage, Log, TEXT("Window is on monitor '%s'"), *Monitor.Name);
			return Monitor.Name;
		}
	}

	// If not found on any monitor, return primary monitor identifier
	if (DisplayMetrics.MonitorInfo.Num() > 0)
	{
		UE_LOG(LogDruidsSage, Warning, TEXT("Window position not found on any monitor, using first monitor '%s'"), *DisplayMetrics.MonitorInfo[0].Name);
		return DisplayMetrics.MonitorInfo[0].Name; // Use first monitor as fallback
	}

	UE_LOG(LogDruidsSage, Error, TEXT("No monitors found!"));
	return TEXT("Unknown");
}

bool FDruidsSageEditorModule::IsPositionOnValidMonitor(const FVector2D& Position, const FVector2D& Size) const
{
	FDisplayMetrics DisplayMetrics;
	FSlateApplication::Get().GetDisplayMetrics(DisplayMetrics);

	UE_LOG(LogDruidsSage, Log, TEXT("Checking if position (%.1f, %.1f) with size (%.1f, %.1f) is on any monitor"),
		Position.X, Position.Y, Size.X, Size.Y);

	// Check if the window would be visible on any monitor
	for (int32 i = 0; i < DisplayMetrics.MonitorInfo.Num(); ++i)
	{
		const FMonitorInfo& Monitor = DisplayMetrics.MonitorInfo[i];
		const FPlatformRect& MonitorRect = Monitor.WorkArea;

		// Calculate window bounds
		float WindowLeft = Position.X;
		float WindowTop = Position.Y;
		float WindowRight = Position.X + Size.X;
		float WindowBottom = Position.Y + Size.Y;

		// Check if rectangles intersect
		bool bIntersects = !(WindowRight < MonitorRect.Left ||
		                    WindowLeft > MonitorRect.Right ||
		                    WindowBottom < MonitorRect.Top ||
		                    WindowTop > MonitorRect.Bottom);

		UE_LOG(LogDruidsSage, Log, TEXT("  Monitor [%d] '%s': Bounds(L=%d, T=%d, R=%d, B=%d), Window(L=%.1f, T=%.1f, R=%.1f, B=%.1f), Intersects=%s"),
			i, *Monitor.Name, MonitorRect.Left, MonitorRect.Top, MonitorRect.Right, MonitorRect.Bottom,
			WindowLeft, WindowTop, WindowRight, WindowBottom, bIntersects ? TEXT("Yes") : TEXT("No"));

		// If any part of the window intersects with this monitor, it's valid
		if (bIntersects)
		{
			UE_LOG(LogDruidsSage, Log, TEXT("Position is valid - found on monitor '%s'"), *Monitor.Name);
			return true;
		}
	}

	UE_LOG(LogDruidsSage, Warning, TEXT("Position is NOT valid - not found on any monitor"));
	return false;
}

void FDruidsSageEditorModule::UpdateActiveExtensions()
{
	TWeakObjectPtr<UObject> ActiveObject = GetActiveObject();
	TArray<TWeakObjectPtr<USageExtension>> ActiveExtensions = FActiveSageExtensions::Get().
		GetActiveExtensionsForContext(ActiveObject);
	TArray<TSharedPtr<FDruidsSageExtensionDefinition>> ActiveExtensionsDefinitions = FActiveSageExtensions::Get().
		GetDefinitionsFromExtensions(ActiveExtensions);

	// Update the chat view with new extensions
	if (ActiveChatView.IsValid())
	{
		ActiveChatView.Get()->SetActiveExtensionDefinitions(ActiveExtensionsDefinitions);
	}
}

void FDruidsSageEditorModule::AddSageExtensionToolbarButton(const FString& MenuName)
{
	UToolMenu* ToolbarMenu = UToolMenus::Get()->ExtendMenu(*MenuName);
	if (ToolbarMenu)
	{
		FToolMenuSection& ToolbarSection = ToolbarMenu->FindOrAddSection("DruidsSage");
		FToolMenuEntry& Entry = ToolbarSection.AddEntry(FToolMenuEntry::InitToolBarButton(
			"OpenSageExtensionEditor",
			FUIAction(
				FExecuteAction::CreateRaw(this, &FDruidsSageEditorModule::OpenSageExtensionEditor),
				FCanExecuteAction::CreateRaw(this, &FDruidsSageEditorModule::IsSageExtensionBlueprintActive),
				FIsActionChecked(),
				FIsActionButtonVisible::CreateRaw(this, &FDruidsSageEditorModule::IsSageExtensionBlueprintActive)
			),
			LOCTEXT("SageExtensionEditorLabel", "Extension Editor"),
			LOCTEXT("SageExtensionEditorTooltip", "Open SageExtension custom editor for the current Blueprint"),
			FSlateIcon(FDruidsSageStyle::GetStyleSetName(), "DruidsSage.ChatIcon") // You can create a custom icon later
		));

		UE_LOG(LogDruidsSage, Log, TEXT("Added SageExtension toolbar button to: %s"), *MenuName);
	}
	else
	{
		UE_LOG(LogDruidsSage, Warning, TEXT("Failed to extend toolbar menu for SageExtension: %s"), *MenuName);
	}
}

bool FDruidsSageEditorModule::IsSageExtensionBlueprintActive() const
{
	// Check if current Blueprint is a SageExtension
	//TODO: COMMENT BACK IN TO DISPLAY THE EDITOR
	if (false && BlueprintHandler.IsValid())
	{
		UBlueprint* ActiveBP = BlueprintHandler->GetActiveBlueprint();
		if (ActiveBP && ActiveBP->ParentClass)
		{
			return ActiveBP->ParentClass->IsChildOf(USageExtension::StaticClass());
		}
	}
	return false;
}

USageExtension* FDruidsSageEditorModule::GetCurrentSageExtension() const
{
	if (BlueprintHandler.IsValid())
	{
		UBlueprint* ActiveBP = BlueprintHandler->GetActiveBlueprint();
		if (ActiveBP && ActiveBP->GeneratedClass && ActiveBP->GeneratedClass->IsChildOf(USageExtension::StaticClass()))
		{
			return ActiveBP->GeneratedClass->GetDefaultObject<USageExtension>();
		}
	}
	return nullptr;
}

UBlueprint* FDruidsSageEditorModule::GetCurrentSageExtensionBlueprint() const
{
	if (BlueprintHandler.IsValid())
	{
		UBlueprint* ActiveBP = BlueprintHandler->GetActiveBlueprint();
		if (ActiveBP && ActiveBP->ParentClass && ActiveBP->ParentClass->IsChildOf(USageExtension::StaticClass()))
		{
			return ActiveBP;
		}
	}
	return nullptr;
}

FString FDruidsSageEditorModule::GetCurrentSageExtensionBlueprintName() const
{
	if (UBlueprint* CurrentBlueprint = GetCurrentSageExtensionBlueprint())
	{
		return CurrentBlueprint->GetName();
	}
	return FString();
}

void FDruidsSageEditorModule::OpenSageExtensionEditor()
{
	TSubclassOf<USageExtensionEditorWidget> WidgetClass = USageExtensionEditorWidget::StaticClass();

	UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
	if (!World)
	{
		UE_LOG(LogDruidsSage_Internal, Warning, TEXT("No valid World context available"));
		return;
	}
	
	// Check for override class
	if (FSageExtensionsModule* SageExtensionsModule = FModuleManager::GetModulePtr<FSageExtensionsModule>("SageExtensions"))
	{
		if (USageExtensionEditorOverrides* Overrides = SageExtensionsModule->GetSageExtensionEditorOverrides())
		{
			if (TSubclassOf<USageExtensionEditorWidget> OverrideClass = Overrides->GetSageExtensionEditorWidgetClass())
			{
				WidgetClass = OverrideClass;
				UE_LOG(LogDruidsSage_Internal, Log, TEXT("Using overridden widget class: %s"), *WidgetClass->GetName());
			}
			else
			{
				UE_LOG(LogDruidsSage_Internal, Log, TEXT("SageExtensionEditorOverrides found but no override class specified, using default: %s"), *WidgetClass->GetName());
			}
		}
		else
		{
			UE_LOG(LogDruidsSage_Internal, Log, TEXT("No SageExtensionEditorOverrides found, using default widget class: %s"), *WidgetClass->GetName());
		}
	}
	else
	{
		UE_LOG(LogDruidsSage_Internal, Warning, TEXT("SageExtensions module not loaded, using default widget class: %s"), *WidgetClass->GetName());
	}
	
	// Ensure WidgetClass is valid
	if (!WidgetClass)
	{
		UE_LOG(LogDruidsSage_Internal, Warning, TEXT("WidgetClass is null"));
		return;
	}
	
	// Use Asset Registry to find the UEditorUtilityWidgetBlueprint asset
	FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
	IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

	// Check if Asset Registry is still loading
	if (AssetRegistry.IsLoadingAssets())
	{
		UE_LOG(LogDruidsSage_Internal, Warning, TEXT("Asset Registry is still loading assets, cannot find UEditorUtilityWidgetBlueprint for class: %s"), *WidgetClass->GetName());
		// Optional: Retry after indexing completes
		AssetRegistry.OnFilesLoaded().AddLambda([this]() { OpenSageExtensionEditor(); });
		return;
	}
	
	TArray<FAssetData> AssetData;
	AssetRegistry.GetAssetsByClass(FTopLevelAssetPath(UEditorUtilityWidgetBlueprint::StaticClass()), AssetData, true);

	UEditorUtilityWidgetBlueprint* WidgetBlueprint = nullptr;
	FString ClassName = WidgetClass->GetName();
	UE_LOG(LogDruidsSage_Internal, Log, TEXT("Searching for UEditorUtilityWidgetBlueprint assets for class: %s"), *ClassName);
	UE_LOG(LogDruidsSage_Internal, Log, TEXT("Found %d EditorUtilityWidgetBlueprint assets"), AssetData.Num());

	for (const FAssetData& Asset : AssetData)
	{
		// Load the Blueprint asset to get its GeneratedClass
		if (UEditorUtilityWidgetBlueprint* TempBlueprint = Cast<UEditorUtilityWidgetBlueprint>(Asset.GetAsset()))
		{
			if (UClass* GeneratedClass = TempBlueprint->GeneratedClass)
			{
				UE_LOG(LogDruidsSage_Internal, Log, TEXT("Checking asset: %s with GeneratedClass: %s"), *Asset.PackageName.ToString(), *GeneratedClass->GetName());
				if (GeneratedClass == WidgetClass || GeneratedClass->IsChildOf(WidgetClass))
				{
					WidgetBlueprint = TempBlueprint;
					UE_LOG(LogDruidsSage_Internal, Log, TEXT("Found matching Blueprint asset for class %s: %s"), *ClassName, *Asset.PackageName.ToString());
					break;
				}
			}
			else
			{
				UE_LOG(LogDruidsSage_Internal, Warning, TEXT("Asset %s has no GeneratedClass"), *Asset.PackageName.ToString());
			}
		}
		else
		{
			UE_LOG(LogDruidsSage_Internal, Warning, TEXT("Failed to load asset: %s"), *Asset.PackageName.ToString());
		}
	}

	if (WidgetBlueprint)
	{
		if (UEditorUtilitySubsystem* EditorUtilitySubsystem = GEditor->GetEditorSubsystem<UEditorUtilitySubsystem>())
		{
			EditorUtilitySubsystem->SpawnAndRegisterTab(WidgetBlueprint);
			UE_LOG(LogDruidsSage, Log, TEXT("Opened SageExtension Editor for Blueprint: %s"),
				BlueprintHandler.IsValid() && BlueprintHandler->GetActiveBlueprint() ?
				*BlueprintHandler->GetActiveBlueprint()->GetName() : TEXT("Unknown"));
		}
		else
		{
			UE_LOG(LogDruidsSage_Internal, Warning, TEXT("Failed to access UEditorUtilitySubsystem"));
		}
	}
	else
	{
		UE_LOG(LogDruidsSage_Internal, Warning, TEXT("Failed to find UEditorUtilityWidgetBlueprint asset for class: %s"), *ClassName);
	}
}

void FDruidsSageEditorModule::CheckBlueprintContextChanges()
{
	// Check if the SageExtension Blueprint state has changed
	bool bCurrentSageExtensionState = IsSageExtensionBlueprintActive();

	if (bCurrentSageExtensionState != bLastSageExtensionState)
	{
		bLastSageExtensionState = bCurrentSageExtensionState;
		RefreshToolbarButtonVisibility();

		UE_LOG(LogDruidsSage, VeryVerbose, TEXT("Blueprint context changed - SageExtension active: %s"),
			bCurrentSageExtensionState ? TEXT("true") : TEXT("false"));
	}
}

void FDruidsSageEditorModule::RefreshToolbarButtonVisibility()
{
	// Force refresh of all toolbar menus to update button visibility
	UToolMenus::Get()->RefreshAllWidgets();

	UE_LOG(LogDruidsSage, VeryVerbose, TEXT("Refreshed toolbar button visibility - SageExtension active: %s"),
		IsSageExtensionBlueprintActive() ? TEXT("true") : TEXT("false"));
}

void FDruidsSageEditorModule::OnWorldCleanup(UWorld* World, bool bSessionEnded, bool bCleanupResources)
{
	// Only handle editor worlds to avoid interfering with PIE or other world types
	if (!World || World->WorldType != EWorldType::Editor)
	{
		return;
	}

	UE_LOG(LogDruidsSage, Log, TEXT("Level switch detected - World cleanup: %s"),
		World ? *World->GetName() : TEXT("Unknown"));

	// Check if chat window is currently open and valid
	bChatWindowWasOpenBeforeLevelSwitch = ChatWindowWeakPtr.IsValid() &&
		ChatWindowWeakPtr.Pin().IsValid() &&
		ChatWindowWeakPtr.Pin()->IsVisible();

	if (bChatWindowWasOpenBeforeLevelSwitch)
	{
		UE_LOG(LogDruidsSage, Log, TEXT("Chat window is open, closing it before level switch to prevent crash"));

		// Mark that we're in a level switch
		bIsLevelSwitchInProgress = true;

		try
		{
			// Save current window state before closing
			SaveCurrentChatWindowState();

			// Close the chat window safely
			if (TSharedPtr<SWindow> ChatWindow = ChatWindowWeakPtr.Pin())
			{
				// Clear UObject references first to prevent GC issues
				if (LastCreatedShell && IsValid(LastCreatedShell))
				{
					// If the shell has any UMG widgets, clear them first
					if (LastCreatedShell->GetChatView())
					{
						// Clear messaging handler references to prevent GC issues
						LastCreatedShell->GetChatView()->ClearAllMessagingHandlerReferences();
						LastCreatedShell->GetChatView()->ClearChat();
					}

					// Release Slate resources to ensure proper cleanup
					LastCreatedShell->ReleaseSlateResources(true);
					LastCreatedShell = nullptr;
				}

				if (ActiveChatView.IsValid())
				{
					// Clear messaging handler references to prevent GC issues
					ActiveChatView->ClearAllMessagingHandlerReferences();

					// Clear any pending requests or references in the chat view
					ActiveChatView->ClearChat();

					// Release Slate resources for the chat view as well
					ActiveChatView->ReleaseSlateResources(true);
					ActiveChatView = nullptr;
				}

				// Request window destruction
				ChatWindow->RequestDestroyWindow();
				ChatWindowWeakPtr.Reset();

				// Use a timer to force garbage collection after a short delay to ensure all cleanup is complete
				if (GEditor)
				{
					UE_LOG(LogDruidsSage, Log, TEXT("Forcing garbage collection after chat window closure"));
					CollectGarbage(GARBAGE_COLLECTION_KEEPFLAGS);

					/*FTimerDelegate GCDelegate;
					GCDelegate.BindLambda([this]()
					{
						UE_LOG(LogDruidsSage, Log, TEXT("Forcing garbage collection after chat window closure"));
						CollectGarbage(GARBAGE_COLLECTION_KEEPFLAGS);
					});

					FTimerHandle GCTimerHandle;
					GEditor->GetTimerManager()->SetTimer(GCTimerHandle, GCDelegate, 0.1f, false);*/
				}

				UE_LOG(LogDruidsSage, Log, TEXT("Chat window closed successfully before level switch"));
			}
		}
		catch (...)
		{
			UE_LOG(LogDruidsSage, Error, TEXT("Exception occurred while closing chat window during level switch"));

			// Try to clear messaging handler references even in error case
			try
			{
				if (LastCreatedShell && IsValid(LastCreatedShell) && LastCreatedShell->GetChatView())
				{
					LastCreatedShell->GetChatView()->ClearAllMessagingHandlerReferences();
				}
				if (ActiveChatView.IsValid())
				{
					ActiveChatView->ClearAllMessagingHandlerReferences();
				}
			}
			catch (...)
			{
				UE_LOG(LogDruidsSage, Error, TEXT("Exception occurred while clearing messaging handler references"));
			}

			// Reset state to prevent issues
			bIsLevelSwitchInProgress = false;
			bChatWindowWasOpenBeforeLevelSwitch = false;
			ChatWindowWeakPtr.Reset();
			LastCreatedShell = nullptr;
			ActiveChatView = nullptr;

			// Force garbage collection even in error case to clean up any dangling references
			UE_LOG(LogDruidsSage, Log, TEXT("Forcing garbage collection after exception during window closure"));
			CollectGarbage(GARBAGE_COLLECTION_KEEPFLAGS);
		}
	}
}

void FDruidsSageEditorModule::OnPostWorldInitialization(UWorld* World, const UWorld::InitializationValues IVS)
{
	// Only handle editor worlds to avoid interfering with PIE or other world types
	if (!World || World->WorldType != EWorldType::Editor)
	{
		return;
	}

	UE_LOG(LogDruidsSage, Log, TEXT("Level switch completed - World initialized: %s"),
		World ? *World->GetName() : TEXT("Unknown"));

	// If we were in a level switch and the chat window was open before, reopen it
	if (bIsLevelSwitchInProgress && bChatWindowWasOpenBeforeLevelSwitch)
	{
		UE_LOG(LogDruidsSage, Log, TEXT("Reopening chat window after level switch"));

		// Use a short delay to ensure the world is fully initialized
		if (GEditor)
		{
			FTimerDelegate ReopenDelegate;
			ReopenDelegate.BindLambda([this]()
			{
				try
				{
					// Double-check that we're not already open
					if (!ChatWindowWeakPtr.IsValid() || !ChatWindowWeakPtr.Pin().IsValid())
					{
						// Reopen the chat window
						TSharedRef<SWindow> NewWindow = CreateFloatingChatWindow();
						ChatWindowWeakPtr = NewWindow;

						UE_LOG(LogDruidsSage, Log, TEXT("Chat window reopened successfully after level switch"));
					}
					else
					{
						UE_LOG(LogDruidsSage, Warning, TEXT("Chat window was already open when trying to reopen after level switch"));
					}
				}
				catch (...)
				{
					UE_LOG(LogDruidsSage, Error, TEXT("Exception occurred while reopening chat window after level switch"));
				}
			});

			FTimerHandle ReopenTimerHandle;
			GEditor->GetTimerManager()->SetTimer(ReopenTimerHandle, ReopenDelegate, 0.5f, false);
		}
		else
		{
			UE_LOG(LogDruidsSage, Warning, TEXT("Cannot reopen chat window - GEditor not available"));
		}
	}

	// Reset level switch state
	bIsLevelSwitchInProgress = false;
	bChatWindowWasOpenBeforeLevelSwitch = false;
}

void FDruidsSageEditorModule::OnWorldChanged(UWorld* OldWorld, UWorld* NewWorld)
{
	// This delegate is less commonly used, but we can log it for debugging
	UE_LOG(LogDruidsSage, VeryVerbose, TEXT("World changed from %s to %s"),
		OldWorld ? *OldWorld->GetName() : TEXT("None"),
		NewWorld ? *NewWorld->GetName() : TEXT("None"));
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FDruidsSageEditorModule, DruidsSageEditorModule)
