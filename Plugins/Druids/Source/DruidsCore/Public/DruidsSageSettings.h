#pragma once

#include <CoreMinimal.h>
#include <Engine/DeveloperSettings.h>
#include "DruidsSageSettings.generated.h"

/**
 *
 */
UCLASS(Config = Plugins, DefaultConfig, meta = (DisplayName = "DruidsSage"))
class DRUIDSCORE_API UDruidsSageSettings : public UDeveloperSettings
{
	GENERATED_BODY()

public:
	explicit UDruidsSageSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

	static const UDruidsSageSettings* Get();

	/* Will print extra internal informations in log */
	UPROPERTY(GlobalConfig, EditAnywhere, Category = "Logging", Meta = (DisplayName = "Enable Internal Logs"))
	bool bEnableInternalLogs;

protected:
#if WITH_EDITOR
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif

	virtual void PostInitProperties() override;

	void SaveAndReload(const FName& PropertyName);

private:
	void ToggleInternalLogs();
};
